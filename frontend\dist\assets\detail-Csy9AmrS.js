const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./request-DoycmXS_.js","./index-Bo9zKuAo.js","./index-DLzI2_8P.css"])))=>i.map(i=>d[i]);
import{_ as K,a3 as te,U as oe,a4 as le,z as ne,a5 as se,J as G,r as F,o as X,c as U,f as g,a,w as t,b as D,h as w,F as P,l as M,t as N,m as C,x as T,E as m,a6 as re,O as ce,p as Z,q as $,e as d,Q as ie,a7 as de,R as _e}from"./index-Bo9zKuAo.js";import{g as H,s as Y}from"./request-DoycmXS_.js";const ge={name:"FileAttachments",components:{Document:se,Download:ne,Upload:le,Delete:oe,Refresh:te},props:{changeData:{type:Object,required:!0},refreshChangeData:{type:Function,default:null}},emits:["update:changeData"],setup(n,{emit:c}){const f="/api/upload_ops_change_file_simple",e={Authorization:`Bearer ${H()}`},u=G({oa_process:!1,signed_archive:!1,operation_sheet:!1,supplementary_material:!1}),j=G({oa_process:!1,signed_archive:!1,operation_sheet:!1,supplementary_material:!1}),I=G({oa_process:!1,signed_archive:!1,operation_sheet:!1,supplementary_material:!1}),J=F(!1),_=F(""),v=F([]),E=F(!1),R=o=>({changeId:n.changeData.change_id,fileType:o,usernameby:localStorage.getItem("username")||"admin"}),z=o=>o?o.split("/").pop():"",O=o=>{if(!["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","image/jpeg","image/png"].includes(o.type))return m.error("文件类型不支持，请上传PDF、Word、Excel或图片文件"),!1;const s=20*1024*1024;return o.size>s?(m.error("文件大小不能超过20MB"),!1):!0},x=async(o=!1,r=!1)=>{try{if(console.log(`开始${o?"重试":""}刷新变更详情数据...${r?"(强制更新模式)":""}`),console.log("当前变更ID:",n.changeData.id),!n.changeData.id)return console.log("变更ID为空，无法刷新数据"),!1;console.log("发送请求获取最新变更数据...");const s=new Date().getTime(),h={id:n.changeData.id,_t:s};console.log("请求参数:",h);const{default:b}=await re(async()=>{const{default:y}=await import("./request-DoycmXS_.js").then(k=>k.r);return{default:y}},__vite__mapDeps([0,1,2]),import.meta.url),p=await b({url:"/api/get_ops_change_management",method:"post",data:h,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}});if(console.log("获取变更数据响应:",JSON.stringify(p,null,2)),p.code===0&&p.msg&&p.msg.length>0){const y=p.msg[0];console.log("获取到的最新数据:",JSON.stringify(y,null,2)),console.log("附件相关字段:","附件1(OA流程):",y.oa_process,y.oa_process_file,"附件2(签字存档):",y.signed_archive,y.signed_archive_file,"附件3(操作表):",y.operation_sheet,"附件4(补充资料):",y.supplementary_material);const k={...n.changeData};k.oa_process=y.oa_process,k.oa_process_file=y.oa_process_file,k.signed_archive=y.signed_archive,k.signed_archive_file=y.signed_archive_file,k.operation_sheet=y.operation_sheet,k.supplementary_material=y.supplementary_material,console.log("更新前的数据:",{oa_process:n.changeData.oa_process,oa_process_file:n.changeData.oa_process_file,signed_archive:n.changeData.signed_archive,signed_archive_file:n.changeData.signed_archive_file,operation_sheet:n.changeData.operation_sheet,supplementary_material:n.changeData.supplementary_material}),console.log("更新后的数据:",{oa_process:k.oa_process,oa_process_file:k.oa_process_file,signed_archive:k.signed_archive,signed_archive_file:k.signed_archive_file,operation_sheet:k.operation_sheet,supplementary_material:k.supplementary_material});const W=n.changeData.oa_process!==k.oa_process||n.changeData.oa_process_file!==k.oa_process_file||n.changeData.signed_archive!==k.signed_archive||n.changeData.signed_archive_file!==k.signed_archive_file||n.changeData.operation_sheet!==k.operation_sheet||n.changeData.supplementary_material!==k.supplementary_material;if(console.log("数据是否有变化:",W),console.log("是否强制更新:",r),W||r){console.log(W?"数据有变化，更新组件数据":"强制更新模式，更新组件数据");const ae=JSON.parse(JSON.stringify(k));return c("update:changeData",ae),o||m.success(W?"附件状态已更新":"刷新成功"),!0}else return console.log("数据没有变化，无需更新"),!1}else return console.log("获取变更数据失败或数据为空"),!1}catch(s){return console.error("刷新变更详情数据失败:",s),!1}},q=async(o,r)=>{try{if(console.log("上传成功处理函数被调用"),console.log("上传响应:",o),o.code===0){m.success("文件上传成功");const s={...n.changeData};r==="oa_process"?(s.oa_process=!0,s.oa_process_file=o.msg.path):r==="signed_archive"?(s.signed_archive=!0,s.signed_archive_file=o.msg.path):r==="operation_sheet"?s.operation_sheet=o.msg.path:r==="supplementary_material"&&(s.supplementary_material=o.msg.path),console.log("更新前的数据:",n.changeData),console.log("更新后的数据:",s),c("update:changeData",s),console.log("组件数据已更新，准备刷新变更详情数据..."),m.info("正在更新附件状态，请稍候..."),setTimeout(async()=>{console.log("开始延迟刷新...");try{if(n.refreshChangeData&&typeof n.refreshChangeData=="function")console.log("使用父组件的刷新方法"),await n.refreshChangeData()?console.log("父组件刷新成功"):(console.log("父组件刷新失败或无变化，尝试第二次刷新"),setTimeout(async()=>{console.log("开始第二次刷新...");try{const b=await n.refreshChangeData();console.log("第二次刷新完成，结果:",b),b||(console.log("两次刷新均未获取到更新的数据，可能需要手动刷新"),m.info("附件状态可能需要手动刷新查看"))}catch(b){console.error("第二次刷新出错:",b)}},2e3));else{console.log("使用组件内部的刷新方法");const h=await x(!1,!1);console.log("第一次刷新完成，结果:",h),h||setTimeout(async()=>{console.log("开始第二次刷新...");try{const b=await x(!0,!1);console.log("第二次刷新完成，结果:",b),b||setTimeout(async()=>{console.log("开始第三次刷新(强制更新模式)...");try{const p=await x(!0,!0);console.log("第三次刷新完成，结果:",p),p||(console.log("三次刷新均未获取到更新的数据，可能需要手动刷新页面"),m.info("附件状态可能需要手动刷新查看"),setTimeout(()=>{const y=JSON.parse(JSON.stringify(n.changeData));c("update:changeData",y)},100))}catch(p){console.error("第三次刷新出错:",p)}},3e3)}catch(b){console.error("第二次刷新出错:",b)}},2e3)}}catch(h){console.error("第一次刷新出错:",h)}},3e3)}else m.error(`上传失败: ${o.msg}`)}catch(s){console.error("上传成功处理函数出错:",s)}finally{u[r]=!1}},L=async(o,r)=>{try{if(u[r]=!0,console.log("开始上传文件:",r),console.log("文件信息:",o.file),console.log("变更ID:",n.changeData.change_id),!n.changeData.change_id)throw new Error("变更ID不能为空，请先保存变更信息");const s=new FormData;s.append("file",o.file);const h=localStorage.getItem("username")||"admin",b=new URLSearchParams({changeId:n.changeData.change_id,fileType:r,usernameby:h}).toString();console.log("表单数据:",{changeId:n.changeData.change_id,fileType:r,usernameby:h});const p=await fetch(`${f}?${b}`,{method:"POST",headers:{Authorization:e.Authorization},body:s}),y=await p.json();if(p.ok)q(y,r);else throw new Error(y.msg||"上传失败")}catch(s){console.error("上传错误:",s),m.error(`上传失败: ${s.message}`),u[r]=!1}},V=o=>{console.error("上传错误:",o),m.error("文件上传失败，请稍后重试"),Object.keys(u).forEach(r=>{u[r]=!1})},i=async o=>{try{j[o]=!0;const r=await fetch(`/api/download_ops_change_file?changeId=${n.changeData.change_id}&fileType=${o}`,{headers:e});if(!r.ok){const h=await r.json();throw new Error(h.msg||"下载失败")}const s=await r.json();if(s.code===0){const h=document.createElement("a");h.href=s.msg.url,h.target="_blank",h.download=z(s.msg.path),document.body.appendChild(h),h.click(),document.body.removeChild(h),m.success("文件下载成功")}else throw new Error(s.msg||"下载失败")}catch(r){console.error("下载错误:",r),m.error(`文件下载失败: ${r.message}`)}finally{j[o]=!1}},B=o=>{ce.confirm("确定要删除此文件吗？此操作不可恢复。","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{I[o]=!0;const r=await fetch("/api/remove_ops_change_file",{method:"POST",headers:{...e,"Content-Type":"application/json"},body:JSON.stringify({changeId:n.changeData.change_id,fileType:o,usernameby:localStorage.getItem("username")||"admin"})});if(!r.ok){const h=await r.json();throw new Error(h.msg||"删除失败")}const s=await r.json();if(s.code===0){m.success("文件删除成功"),console.log("文件删除成功，准备更新组件数据");const h={...n.changeData};o==="oa_process"?(h.oa_process=!1,h.oa_process_file=null):o==="signed_archive"?(h.signed_archive=!1,h.signed_archive_file=null):o==="operation_sheet"?h.operation_sheet=null:o==="supplementary_material"&&(h.supplementary_material=null),console.log("更新前的数据:",n.changeData),console.log("更新后的数据:",h),c("update:changeData",h),console.log("组件数据已更新，准备刷新变更详情数据..."),m.info("正在更新附件状态，请稍候..."),setTimeout(async()=>{console.log("开始延迟刷新...");try{if(n.refreshChangeData&&typeof n.refreshChangeData=="function")console.log("使用父组件的刷新方法"),await n.refreshChangeData()?console.log("父组件刷新成功"):(console.log("父组件刷新失败或无变化，尝试第二次刷新"),setTimeout(async()=>{console.log("开始第二次刷新...");try{const p=await n.refreshChangeData();console.log("第二次刷新完成，结果:",p),p||(console.log("两次刷新均未获取到更新的数据，可能需要手动刷新"),m.info("附件状态可能需要手动刷新查看"))}catch(p){console.error("第二次刷新出错:",p)}},2e3));else{console.log("使用组件内部的刷新方法");const b=await x(!1,!1);console.log("第一次刷新完成，结果:",b),b||setTimeout(async()=>{console.log("开始第二次刷新...");try{const p=await x(!0,!1);console.log("第二次刷新完成，结果:",p),p||setTimeout(async()=>{console.log("开始第三次刷新(强制更新模式)...");try{const y=await x(!0,!0);console.log("第三次刷新完成，结果:",y),y||(console.log("三次刷新均未获取到更新的数据，可能需要手动刷新页面"),m.info("附件状态可能需要手动刷新查看"),setTimeout(()=>{const k=JSON.parse(JSON.stringify(n.changeData));c("update:changeData",k)},100))}catch(y){console.error("第三次刷新出错:",y)}},3e3)}catch(p){console.error("第二次刷新出错:",p)}},2e3)}}catch(b){console.error("第一次刷新出错:",b)}},3e3)}else throw new Error(s.msg||"删除失败")}catch(r){console.error("删除错误:",r),m.error(`文件删除失败: ${r.message}`)}finally{I[o]=!1}}).catch(()=>{})},Q=async()=>{try{J.value=!0,m.info("正在刷新附件状态..."),n.refreshChangeData&&typeof n.refreshChangeData=="function"?(console.log("使用父组件的刷新方法"),await n.refreshChangeData()?console.log("父组件刷新成功"):(console.log("父组件刷新失败或无变化"),m.info("刷新完成，未检测到附件状态变化"))):(console.log("使用组件内部的刷新方法"),await x(!1,!0)||m.info("刷新完成，未检测到附件状态变化"),setTimeout(()=>{const r=JSON.parse(JSON.stringify(n.changeData));c("update:changeData",r)},100))}catch(o){console.error("手动刷新出错:",o),m.error("刷新失败，请稍后重试")}finally{J.value=!1}},l=async()=>{try{const o=await Y({url:"/api/get_ops_change_templates",method:"post",data:{currentPage:1,pageSize:100}});o.code===0?(v.value=o.msg||[],console.log("获取模板列表成功:",v.value)):console.error("获取模板列表失败:",o.msg)}catch(o){console.error("获取模板列表失败:",o)}},S=async()=>{if(!_.value){m.warning("请先选择要下载的模板");return}try{E.value=!0,console.log("开始下载模板，ID:",_.value);const o=v.value.find(b=>b.id===_.value);if(!o){m.error("未找到选中的模板");return}const r=`/api/download_ops_change_template?id=${_.value}&direct=true`,s=document.createElement("a");s.href=r,s.target="_blank",s.download=o.original_filename||o.template_name,H()?window.open(r,"_blank"):(document.body.appendChild(s),s.click(),document.body.removeChild(s)),m.success(`模板 "${o.template_name}" 下载成功`),_.value=""}catch(o){console.error("下载模板失败:",o),m.error("模板下载失败，请稍后重试")}finally{E.value=!1}};return X(()=>{l()}),{uploadUrl:f,headers:e,uploadLoading:u,downloadLoading:j,removeLoading:I,refreshLoading:J,selectedTemplate:_,templateOptions:v,templateDownloadLoading:E,getUploadData:R,getFileName:z,beforeUpload:O,handleUploadSuccess:q,handleUploadError:V,customUpload:L,downloadFile:i,removeFile:B,refreshChangeData:x,handleManualRefresh:Q,getTemplateList:l,downloadTemplate:S}}},A=n=>(Z("data-v-38915671"),n=n(),$(),n),he={class:"file-attachments-container"},me={class:"attachments-header"},ue=A(()=>g("span",{class:"divider-title"},"附件管理",-1)),fe={class:"template-download-section"},pe={class:"template-content"},ye={class:"template-selector"},ve=A(()=>g("span",{class:"template-title"},"变更操作表模板下载",-1)),De={class:"card-header"},we=A(()=>g("span",null,"OA流程",-1)),be={class:"card-content"},ke={key:0,class:"file-info"},Ce={class:"file-name"},xe={key:1,class:"file-placeholder"},Ue=A(()=>g("span",null,"请上传OA流程文件",-1)),ze={class:"action-buttons"},Le={class:"card-header"},Oe=A(()=>g("span",null,"签字存档",-1)),Se={class:"card-content"},Ee={key:0,class:"file-info"},Ie={class:"file-name"},Re={key:1,class:"file-placeholder"},qe=A(()=>g("span",null,"请上传签字存档文件",-1)),Ve={class:"action-buttons"},Ne={class:"card-header"},Fe=A(()=>g("span",null,"变更操作表",-1)),Te={class:"card-content"},Ae={key:0,class:"file-info"},Je={class:"file-name"},je={key:1,class:"file-placeholder"},Be=A(()=>g("span",null,"请上传变更操作表文件",-1)),Pe={class:"action-buttons"},Me={class:"card-header"},Ye=A(()=>g("span",null,"补充资料",-1)),Qe={class:"card-content"},We={key:0,class:"file-info"},Ge={class:"file-name"},He={key:1,class:"file-placeholder"},Ke=A(()=>g("span",null,"请上传补充资料文件",-1)),Xe={class:"action-buttons"};function Ze(n,c,f,e,u,j){const I=D("el-divider"),J=D("Refresh"),_=D("el-icon"),v=D("el-button"),E=D("el-option"),R=D("el-select"),z=D("Download"),O=D("el-card"),x=D("el-tag"),q=D("Document"),L=D("Upload"),V=D("el-upload"),i=D("Delete"),B=D("el-col"),Q=D("el-row");return d(),U("div",he,[g("div",me,[a(I,{"content-position":"left"},{default:t(()=>[ue]),_:1}),a(v,{type:"primary",size:"small",class:"refresh-button",loading:e.refreshLoading,onClick:e.handleManualRefresh},{default:t(()=>[a(_,null,{default:t(()=>[a(J)]),_:1}),w(" 刷新附件状态 ")]),_:1},8,["loading","onClick"])]),g("div",fe,[a(O,{shadow:"hover",class:"template-card"},{default:t(()=>[g("div",pe,[g("div",ye,[ve,a(R,{modelValue:e.selectedTemplate,"onUpdate:modelValue":c[0]||(c[0]=l=>e.selectedTemplate=l),placeholder:"请选择变更操作表模板",style:{width:"50%"},size:"small",filterable:"",clearable:""},{default:t(()=>[(d(!0),U(P,null,M(e.templateOptions,l=>(d(),C(E,{key:l.id,label:l.template_name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),a(v,{type:"primary",size:"small",onClick:e.downloadTemplate,disabled:!e.selectedTemplate,loading:e.templateDownloadLoading,style:{"margin-left":"10px"}},{default:t(()=>[a(_,null,{default:t(()=>[a(z)]),_:1}),w(" 下载模板 ")]),_:1},8,["onClick","disabled","loading"])])])]),_:1})]),a(Q,{gutter:20},{default:t(()=>[a(B,{xs:24,sm:12,md:6,lg:6,xl:6},{default:t(()=>[a(O,{shadow:"hover",class:"attachment-card"},{header:t(()=>[g("div",De,[we,f.changeData.oa_process?(d(),C(x,{key:0,type:"success",size:"small"},{default:t(()=>[w("已上传")]),_:1})):(d(),C(x,{key:1,type:"info",size:"small"},{default:t(()=>[w("未上传")]),_:1}))])]),default:t(()=>[g("div",be,[f.changeData.oa_process?(d(),U("div",ke,[a(_,null,{default:t(()=>[a(q)]),_:1}),g("span",Ce,N(e.getFileName(f.changeData.oa_process_file)),1)])):(d(),U("div",xe,[a(_,null,{default:t(()=>[a(L)]),_:1}),Ue])),g("div",ze,[f.changeData.oa_process?(d(),C(v,{key:0,type:"primary",size:"small",onClick:c[1]||(c[1]=l=>e.downloadFile("oa_process")),loading:e.downloadLoading.oa_process},{default:t(()=>[a(_,null,{default:t(()=>[a(z)]),_:1}),w(" 下载 ")]),_:1},8,["loading"])):T("",!0),a(V,{class:"upload-button",action:e.uploadUrl,headers:e.headers,data:e.getUploadData("oa_process"),"show-file-list":!1,"before-upload":e.beforeUpload,"http-request":l=>e.customUpload(l,"oa_process")},{default:t(()=>[a(v,{type:"success",size:"small",loading:e.uploadLoading.oa_process},{default:t(()=>[a(_,null,{default:t(()=>[a(L)]),_:1}),w(" "+N(f.changeData.oa_process?"重新上传":"上传"),1)]),_:1},8,["loading"])]),_:1},8,["action","headers","data","before-upload","http-request"]),f.changeData.oa_process?(d(),C(v,{key:1,type:"danger",size:"small",onClick:c[2]||(c[2]=l=>e.removeFile("oa_process")),loading:e.removeLoading.oa_process},{default:t(()=>[a(_,null,{default:t(()=>[a(i)]),_:1}),w(" 删除 ")]),_:1},8,["loading"])):T("",!0)])])]),_:1})]),_:1}),a(B,{xs:24,sm:12,md:6,lg:6,xl:6},{default:t(()=>[a(O,{shadow:"hover",class:"attachment-card"},{header:t(()=>[g("div",Le,[Oe,f.changeData.signed_archive?(d(),C(x,{key:0,type:"success",size:"small"},{default:t(()=>[w("已上传")]),_:1})):(d(),C(x,{key:1,type:"info",size:"small"},{default:t(()=>[w("未上传")]),_:1}))])]),default:t(()=>[g("div",Se,[f.changeData.signed_archive?(d(),U("div",Ee,[a(_,null,{default:t(()=>[a(q)]),_:1}),g("span",Ie,N(e.getFileName(f.changeData.signed_archive_file)),1)])):(d(),U("div",Re,[a(_,null,{default:t(()=>[a(L)]),_:1}),qe])),g("div",Ve,[f.changeData.signed_archive?(d(),C(v,{key:0,type:"primary",size:"small",onClick:c[3]||(c[3]=l=>e.downloadFile("signed_archive")),loading:e.downloadLoading.signed_archive},{default:t(()=>[a(_,null,{default:t(()=>[a(z)]),_:1}),w(" 下载 ")]),_:1},8,["loading"])):T("",!0),a(V,{class:"upload-button",action:e.uploadUrl,headers:e.headers,data:e.getUploadData("signed_archive"),"show-file-list":!1,"before-upload":e.beforeUpload,"http-request":l=>e.customUpload(l,"signed_archive")},{default:t(()=>[a(v,{type:"success",size:"small",loading:e.uploadLoading.signed_archive},{default:t(()=>[a(_,null,{default:t(()=>[a(L)]),_:1}),w(" "+N(f.changeData.signed_archive?"重新上传":"上传"),1)]),_:1},8,["loading"])]),_:1},8,["action","headers","data","before-upload","http-request"]),f.changeData.signed_archive?(d(),C(v,{key:1,type:"danger",size:"small",onClick:c[4]||(c[4]=l=>e.removeFile("signed_archive")),loading:e.removeLoading.signed_archive},{default:t(()=>[a(_,null,{default:t(()=>[a(i)]),_:1}),w(" 删除 ")]),_:1},8,["loading"])):T("",!0)])])]),_:1})]),_:1}),a(B,{xs:24,sm:12,md:6,lg:6,xl:6},{default:t(()=>[a(O,{shadow:"hover",class:"attachment-card"},{header:t(()=>[g("div",Ne,[Fe,f.changeData.operation_sheet?(d(),C(x,{key:0,type:"success",size:"small"},{default:t(()=>[w("已上传")]),_:1})):(d(),C(x,{key:1,type:"info",size:"small"},{default:t(()=>[w("未上传")]),_:1}))])]),default:t(()=>[g("div",Te,[f.changeData.operation_sheet?(d(),U("div",Ae,[a(_,null,{default:t(()=>[a(q)]),_:1}),g("span",Je,N(e.getFileName(f.changeData.operation_sheet)),1)])):(d(),U("div",je,[a(_,null,{default:t(()=>[a(L)]),_:1}),Be])),g("div",Pe,[f.changeData.operation_sheet?(d(),C(v,{key:0,type:"primary",size:"small",onClick:c[5]||(c[5]=l=>e.downloadFile("operation_sheet")),loading:e.downloadLoading.operation_sheet},{default:t(()=>[a(_,null,{default:t(()=>[a(z)]),_:1}),w(" 下载 ")]),_:1},8,["loading"])):T("",!0),a(V,{class:"upload-button",action:e.uploadUrl,headers:e.headers,data:e.getUploadData("operation_sheet"),"show-file-list":!1,"before-upload":e.beforeUpload,"http-request":l=>e.customUpload(l,"operation_sheet")},{default:t(()=>[a(v,{type:"success",size:"small",loading:e.uploadLoading.operation_sheet},{default:t(()=>[a(_,null,{default:t(()=>[a(L)]),_:1}),w(" "+N(f.changeData.operation_sheet?"重新上传":"上传"),1)]),_:1},8,["loading"])]),_:1},8,["action","headers","data","before-upload","http-request"]),f.changeData.operation_sheet?(d(),C(v,{key:1,type:"danger",size:"small",onClick:c[6]||(c[6]=l=>e.removeFile("operation_sheet")),loading:e.removeLoading.operation_sheet},{default:t(()=>[a(_,null,{default:t(()=>[a(i)]),_:1}),w(" 删除 ")]),_:1},8,["loading"])):T("",!0)])])]),_:1})]),_:1}),a(B,{xs:24,sm:12,md:6,lg:6,xl:6},{default:t(()=>[a(O,{shadow:"hover",class:"attachment-card"},{header:t(()=>[g("div",Me,[Ye,f.changeData.supplementary_material?(d(),C(x,{key:0,type:"success",size:"small"},{default:t(()=>[w("已上传")]),_:1})):(d(),C(x,{key:1,type:"info",size:"small"},{default:t(()=>[w("未上传")]),_:1}))])]),default:t(()=>[g("div",Qe,[f.changeData.supplementary_material?(d(),U("div",We,[a(_,null,{default:t(()=>[a(q)]),_:1}),g("span",Ge,N(e.getFileName(f.changeData.supplementary_material)),1)])):(d(),U("div",He,[a(_,null,{default:t(()=>[a(L)]),_:1}),Ke])),g("div",Xe,[f.changeData.supplementary_material?(d(),C(v,{key:0,type:"primary",size:"small",onClick:c[7]||(c[7]=l=>e.downloadFile("supplementary_material")),loading:e.downloadLoading.supplementary_material},{default:t(()=>[a(_,null,{default:t(()=>[a(z)]),_:1}),w(" 下载 ")]),_:1},8,["loading"])):T("",!0),a(V,{class:"upload-button",action:e.uploadUrl,headers:e.headers,data:e.getUploadData("supplementary_material"),"show-file-list":!1,"before-upload":e.beforeUpload,"http-request":l=>e.customUpload(l,"supplementary_material")},{default:t(()=>[a(v,{type:"success",size:"small",loading:e.uploadLoading.supplementary_material},{default:t(()=>[a(_,null,{default:t(()=>[a(L)]),_:1}),w(" "+N(f.changeData.supplementary_material?"重新上传":"上传"),1)]),_:1},8,["loading"])]),_:1},8,["action","headers","data","before-upload","http-request"]),f.changeData.supplementary_material?(d(),C(v,{key:1,type:"danger",size:"small",onClick:c[8]||(c[8]=l=>e.removeFile("supplementary_material")),loading:e.removeLoading.supplementary_material},{default:t(()=>[a(_,null,{default:t(()=>[a(i)]),_:1}),w(" 删除 ")]),_:1},8,["loading"])):T("",!0)])])]),_:1})]),_:1})]),_:1})])}const $e=K(ge,[["render",Ze],["__scopeId","data-v-38915671"]]),ea={name:"ChangeManagementDetail",components:{FileAttachments:$e},setup(){const n=ie(),c=_e(),f=F(null),e=F(!1),u=G({id:null,change_id:"",title:"",system:"",change_level:"",planned_change_time:"",requester:"",implementers:"",oa_process:!1,oa_process_file:null,signed_archive:!1,signed_archive_file:null,operation_sheet:null,supplementary_material:null}),j=F([]),I=F([]),J=l=>{u.implementers=l.join(",")},_=l=>{u.system=l.join(",")},v=l=>{if(!l)return"未知用户";const S=O.value.find(o=>o.username===l);return S?S.real_name:l},E={title:[{required:!0,message:"请输入变更名称",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],system:[{required:!0,message:"请选择变更系统",trigger:"change"}],change_level:[{required:!0,message:"请选择变更级别",trigger:"change"}],planned_change_time:[{required:!0,message:"请选择计划变更时间",trigger:"change"}],requester:[{required:!0,message:"请选择变更负责人",trigger:"change"}],implementers:[{required:!0,message:"请选择变更实施人",trigger:"change"}]},R=F([]),z=F([]),O=F([]),x=async()=>{try{const l=await Y({url:"/api/get_system_list",method:"post"});l.code===0&&(R.value=l.msg)}catch(l){console.error("获取系统列表失败:",l),m.error("获取系统列表失败")}},q=async()=>{try{const l=await Y({url:"/api/get_cmdb_data_dictionary",method:"post",data:{dict_type:"P"}});l.code===0&&(z.value=l.msg)}catch(l){console.error("获取变更级别列表失败:",l),m.error("获取变更级别列表失败")}},L=async()=>{try{const l=await Y({url:"/api/get_user_list",method:"post"});l.code===0&&(O.value=l.msg)}catch(l){console.error("获取用户列表失败:",l),m.error("获取用户列表失败")}},V=async(l,S=!0,o=!0)=>{let r=null;S&&(r=de.service({lock:!0,text:"加载中...",background:"rgba(0, 0, 0, 0.7)"}));try{console.log("获取变更详情，ID:",l);const s=new Date().getTime(),h=await Y({url:"/api/get_ops_change_management",method:"post",data:{id:l,_t:s},headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}});if(console.log("获取变更详情响应:",h),h.code===0&&h.msg.length>0){const b=h.msg[0];console.log("获取到的变更数据:",b);const p={};return Object.keys(u).forEach(y=>{b[y]!==void 0?p[y]=b[y]:p[y]=u[y]}),console.log("更新前的数据:",JSON.stringify({oa_process:u.oa_process,oa_process_file:u.oa_process_file,signed_archive:u.signed_archive,signed_archive_file:u.signed_archive_file,operation_sheet:u.operation_sheet})),console.log("更新后的数据:",JSON.stringify({oa_process:p.oa_process,oa_process_file:p.oa_process_file,signed_archive:p.signed_archive,signed_archive_file:p.signed_archive_file,operation_sheet:p.operation_sheet})),Object.assign(u,p),u.implementers&&(j.value=u.implementers.split(",")),u.system&&(I.value=u.system.split(",")),o&&m.success("数据已刷新"),!0}else return o&&m.error("未找到变更记录"),c.push("/ops_change_management"),!1}catch(s){return console.error("获取变更详情失败:",s),o&&m.error("获取变更详情失败"),c.push("/ops_change_management"),!1}finally{r&&r.close()}},i=async()=>(console.log("刷新变更详情数据"),u.id?await V(u.id,!1,!0):(console.log("变更ID为空，无法刷新数据"),!1)),B=async()=>{f.value&&await f.value.validate(async l=>{if(!l){m.error("请检查表单填写是否正确");return}e.value=!0;try{const S=u.id?"/api/update_ops_change_management":"/api/add_ops_change_management",o=localStorage.getItem("loginUsername")||"admin",r={...u,changeLevel:u.change_level,plannedChangeTime:u.planned_change_time,usernameby:o,created_by:o,updated_by:o};console.log("发送请求数据:",r);const s=await Y({url:S,method:"post",data:r});s.code===0?(m.success("保存成功"),u.id||(u.id=s.msg.id,u.change_id=s.msg.change_id)):m.error(`保存失败: ${s.msg}`)}catch(S){console.error("保存变更失败:",S),m.error("保存变更失败")}finally{e.value=!1}})},Q=()=>{c.push("/ops_change_management")};return X(async()=>{await Promise.all([x(),q(),L()]);const l=n.params.id;if(l&&l!=="new")await V(l);else{const S=localStorage.getItem("username");S&&(u.requester=S)}}),{formRef:f,changeData:u,implementersArray:j,systemArray:I,rules:E,systemOptions:R,changeLevelOptions:z,userOptions:O,saveLoading:e,handleImplementersChange:J,handleSystemChange:_,getUserRealName:v,saveChange:B,goBack:Q,refreshChangeData:i}}},ee=n=>(Z("data-v-3b9fd8c2"),n=n(),$(),n),aa={class:"app-container"},ta={class:"card-header"},oa=ee(()=>g("span",null,"变更详情",-1)),la={class:"header-buttons"},na={class:"change-id-section"},sa={class:"change-id-text"},ra={class:"change-info-card"},ca=ee(()=>g("div",{class:"card-title"},"变更信息",-1)),ia={class:"card-content"},da={key:0,class:"tag-display"},_a={key:0,class:"tag-display"};function ga(n,c,f,e,u,j){const I=D("el-button"),J=D("el-input"),_=D("el-form-item"),v=D("el-col"),E=D("el-option"),R=D("el-select"),z=D("el-row"),O=D("el-date-picker"),x=D("el-tag"),q=D("el-form"),L=D("file-attachments"),V=D("el-card");return d(),U("div",aa,[a(V,{class:"box-card"},{header:t(()=>[g("div",ta,[oa,g("div",la,[a(I,{type:"primary",onClick:e.goBack},{default:t(()=>[w("返回列表")]),_:1},8,["onClick"]),a(I,{type:"success",onClick:e.saveChange,loading:e.saveLoading},{default:t(()=>[w("保存")]),_:1},8,["onClick","loading"])])])]),default:t(()=>[g("div",na,[g("span",sa,"变更编号："+N(e.changeData.change_id||"系统自动生成"),1)]),g("div",ra,[ca,g("div",ia,[a(q,{ref:"formRef",model:e.changeData,rules:e.rules,"label-width":"100px","label-position":"right"},{default:t(()=>[a(z,{gutter:16},{default:t(()=>[a(v,{xs:24,sm:12,md:12,lg:12,xl:12},{default:t(()=>[a(_,{label:"变更名称",prop:"title"},{default:t(()=>[a(J,{modelValue:e.changeData.title,"onUpdate:modelValue":c[0]||(c[0]=i=>e.changeData.title=i),placeholder:"请输入变更名称",size:"small"},null,8,["modelValue"])]),_:1})]),_:1}),a(v,{xs:24,sm:12,md:12,lg:12,xl:12},{default:t(()=>[a(_,{label:"变更级别",prop:"change_level"},{default:t(()=>[a(R,{modelValue:e.changeData.change_level,"onUpdate:modelValue":c[1]||(c[1]=i=>e.changeData.change_level=i),filterable:"",placeholder:"请选择变更级别",style:{width:"100%"},size:"small"},{default:t(()=>[(d(!0),U(P,null,M(e.changeLevelOptions,i=>(d(),C(E,{key:i.dict_code,label:i.dict_name,value:i.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(z,{gutter:16},{default:t(()=>[a(v,{xs:24,sm:12,md:12,lg:12,xl:12},{default:t(()=>[a(_,{label:"计划时间",prop:"planned_change_time"},{default:t(()=>[a(O,{modelValue:e.changeData.planned_change_time,"onUpdate:modelValue":c[2]||(c[2]=i=>e.changeData.planned_change_time=i),type:"date",placeholder:"选择日期",style:{width:"100%"},"value-format":"YYYY-MM-DD",size:"small"},null,8,["modelValue"])]),_:1})]),_:1}),a(v,{xs:24,sm:12,md:12,lg:12,xl:12},{default:t(()=>[a(_,{label:"变更负责人",prop:"requester"},{default:t(()=>[a(R,{modelValue:e.changeData.requester,"onUpdate:modelValue":c[3]||(c[3]=i=>e.changeData.requester=i),filterable:"",placeholder:"请选择负责人",style:{width:"100%"},size:"small"},{default:t(()=>[(d(!0),U(P,null,M(e.userOptions,i=>(d(),C(E,{key:i.username,label:i.real_name,value:i.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(z,{gutter:16},{default:t(()=>[a(v,{xs:24,sm:12,md:12,lg:12,xl:12},{default:t(()=>[a(_,{label:"变更系统",prop:"system"},{default:t(()=>[a(R,{modelValue:e.systemArray,"onUpdate:modelValue":c[4]||(c[4]=i=>e.systemArray=i),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",filterable:"",placeholder:"请选择变更系统",style:{width:"100%"},size:"small",onChange:e.handleSystemChange},{default:t(()=>[(d(!0),U(P,null,M(e.systemOptions,i=>(d(),C(E,{key:i.system_abbreviation,label:i.system_abbreviation,value:i.system_abbreviation},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"]),e.systemArray.length>0?(d(),U("div",da,[(d(!0),U(P,null,M(e.systemArray,i=>(d(),C(x,{key:i,class:"display-tag",type:"primary",effect:"light",size:"small"},{default:t(()=>[w(N(i),1)]),_:2},1024))),128))])):T("",!0)]),_:1})]),_:1}),a(v,{xs:24,sm:12,md:12,lg:12,xl:12},{default:t(()=>[a(_,{label:"变更实施人",prop:"implementers"},{default:t(()=>[a(R,{modelValue:e.implementersArray,"onUpdate:modelValue":c[5]||(c[5]=i=>e.implementersArray=i),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",filterable:"",placeholder:"请选择实施人",style:{width:"100%"},size:"small",onChange:e.handleImplementersChange},{default:t(()=>[(d(!0),U(P,null,M(e.userOptions,i=>(d(),C(E,{key:i.username,label:i.real_name,value:i.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"]),e.implementersArray.length>0?(d(),U("div",_a,[(d(!0),U(P,null,M(e.implementersArray,i=>(d(),C(x,{key:i,class:"display-tag",type:"primary",effect:"light",size:"small"},{default:t(()=>[w(N(e.getUserRealName(i)),1)]),_:2},1024))),128))])):T("",!0)]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])])]),a(L,{changeData:e.changeData,"onUpdate:changeData":c[6]||(c[6]=i=>e.changeData=i),refreshChangeData:e.refreshChangeData},null,8,["changeData","refreshChangeData"])]),_:1})])}const ua=K(ea,[["render",ga],["__scopeId","data-v-3b9fd8c2"]]);export{ua as default};
