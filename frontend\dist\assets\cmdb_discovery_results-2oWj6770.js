import{_ as L,I as M,A as O,z as j,c as D,a as t,f as h,w as s,b as d,h as r,t as u,B as H,C as Y,m as y,p as G,q as J,e as m}from"./index-C0WPPkX4.js";import{u as V,w as K,F as Q}from"./FileSaver.min-BAET_C7X.js";const W={name:"cmdb_discovery_results",components:{Download:j,Plus:O,Back:M},data(){return{resultList:[],selectedResults:[],loading:!1,dialogVisible:{register:!1,details:!1},search:{ip_address:"",hostname:"",task_id:"",cmdb_status:"",total:0,pageSize:10,currentPage:1,sortProp:"discovery_time",sortOrder:"desc"},registerForm:{assetType:"device",ip_address:"",hostname:"",function_purpose:"",admin1:"",admin2:"",data_center:"",business_system_name:"",deployed_applications:""},registerRules:{assetType:[{required:!0,message:"请选择资产类型",trigger:"change"}]},detailsData:{},datacenters:[],initialTaskId:""}},created(){const a=this.$route.query.task_id;a&&(this.search.task_id=a,this.initialTaskId=a),this.loadData(),this.getDatadict("A","datacenters")},methods:{async loadData(){try{this.loading=!0;const a=await this.$axios.post("/api/discovery/get_discovery_results",this.search);this.resultList=a.data.msg,this.search.total=a.data.total}catch(a){console.error("加载结果列表失败:",a),this.$message.error("加载结果列表失败")}finally{this.loading=!1}},resetSearch(){this.search={ip_address:"",hostname:"",task_id:this.initialTaskId||"",cmdb_status:"",total:0,pageSize:10,currentPage:1,sortProp:"discovery_time",sortOrder:"desc"},this.loadData()},handlePageChange(a){this.search.currentPage=a,this.loadData()},handlePageSizeChange(a){this.search.pageSize=a,this.search.currentPage=1,this.loadData()},handleSortChange({prop:a,order:e}){a&&(this.search.sortProp=a,this.search.sortOrder=e==="ascending"?"asc":"desc",this.loadData())},handleSelectionChange(a){this.selectedResults=a},goBack(){this.$router.push("/cmdb_discovery_tasks")},formatDateTime(a){if(!a)return"";if(typeof a=="string"&&a.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/))return a;try{const e=new Date(a);if(isNaN(e.getTime()))return a;const n=e.getFullYear(),c=String(e.getMonth()+1).padStart(2,"0"),l=String(e.getDate()).padStart(2,"0"),i=String(e.getHours()).padStart(2,"0"),g=String(e.getMinutes()).padStart(2,"0"),_=String(e.getSeconds()).padStart(2,"0");return`${n}-${c}-${l} ${i}:${g}:${_}`}catch(e){return console.error("Date formatting error:",e),a}},exportData(){const e=this.$refs.table.columns.filter(p=>p.type!=="selection"),n=e.map(p=>p.label),c=this.resultList.map(p=>e.map(k=>k.property==="cmdb_status"?p[k.property]==="已登记"?"已登记":"未登记":k.property==="discovery_time"?this.formatDateTime(p[k.property]):p[k.property]||"")),l=[n,...c],i=V.aoa_to_sheet(l),g=V.book_new();V.book_append_sheet(g,i,"发现结果");const _=K(g,{bookType:"xlsx",type:"array"}),v=new Blob([_],{type:"application/octet-stream"});Q.saveAs(v,"自动发现结果.xlsx")},handleViewDetails(a){this.detailsData={...a},this.dialogVisible.details=!0},handleRegister(a){if(a.cmdb_status==="已登记"){this.$message.warning("该资产已登记");return}this.registerForm={assetType:"ip_asset",ip_address:a.ip_address,hostname:a.hostname||"",function_purpose:"",admin1:"",admin2:"",data_center:"",business_system_name:"",deployed_applications:a.open_ports?`开放端口: ${a.open_ports}`:""},this.dialogVisible.register=!0},navigateToAssetForm(){if(this.registerForm.assetType==="ip_asset"){this.submitIpAssetRegister();return}this.dialogVisible.register=!1;let a="",e={from_discovery:"true",ip_address:this.registerForm.ip_address,hostname:this.registerForm.hostname,open_ports:this.registerForm.deployed_applications};switch(this.registerForm.assetType){case"device":a="/cmdb_device_management";break;case"server":a="/cmdb_server_management";break;case"vm":a="/cmdb_vm_registry";break;case"application":a="/cmdb_application_system_info";break;default:a="/cmdb_device_management"}this.$router.push({path:a,query:e})},async submitIpAssetRegister(){var a,e;try{const n=localStorage.getItem("loginUsername")||"unknown",c={management_ip:this.registerForm.ip_address,designated_admin:this.registerForm.admin1||null,remarks:"自动发现登记",usernameby:n};await this.$axios.post("/api/add_cmdb_host_scan_results",c),this.$message.success("IP资产登记成功"),this.dialogVisible.register=!1,setTimeout(()=>{this.loadData()},1e3)}catch(n){console.error("IP资产登记失败:",n),this.$message.error(((e=(a=n.response)==null?void 0:a.data)==null?void 0:e.msg)||"IP资产登记失败")}},registerSelected(){if(this.selectedResults.length===0){this.$message.warning("请先选择要登记的资产");return}if(this.selectedResults.some(e=>e.cmdb_status==="已登记")){this.$message.warning("选中的资产中包含已登记的资产，请重新选择");return}if(this.selectedResults.length===1){this.handleRegister(this.selectedResults[0]);return}this.$confirm(`确定要批量登记 ${this.selectedResults.length} 个资产到IP资产管理吗？`,"批量登记",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{await this.batchRegisterIpAssets()}).catch(()=>{this.$message.info("已取消批量登记")})},async getDatadict(a,e,n=0){try{const c=await this.$axios.post("/api/get_cmdb_data_dictionary",{dict_code:a},{timeout:3e4});this[e]=c.data.msg}catch(c){if(console.error("获取数据字典失败:",c),n<3){console.log(`正在重试获取数据字典 (${n+1}/3)...`),setTimeout(()=>{this.getDatadict(a,e,n+1)},1e3);return}this.$message.error("获取数据字典失败"),e==="datacenters"&&(!this[e]||this[e].length===0)&&(this[e]=[{dict_code:"default",dict_name:"默认机房"}])}},async batchRegisterIpAssets(){try{const a=localStorage.getItem("loginUsername")||"unknown",e=this.selectedResults.length;let n=0,c=0;const l=this.$loading({lock:!0,text:`正在批量登记IP资产 (0/${e})`,spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});for(let i=0;i<this.selectedResults.length;i++){const g=this.selectedResults[i];l.text=`正在批量登记IP资产 (${i+1}/${e})`;try{const _={management_ip:g.ip_address,designated_admin:null,remarks:"自动发现批量登记",usernameby:a};await this.$axios.post("/api/add_cmdb_host_scan_results",_),n++}catch(_){console.error(`登记IP资产 ${g.ip_address} 失败:`,_),c++}}l.close(),c===0?this.$message.success(`成功登记 ${n} 个IP资产`):this.$message.warning(`登记完成，成功: ${n}，失败: ${c}`),setTimeout(()=>{this.loadData()},1e3)}catch(a){console.error("批量登记IP资产失败:",a),this.$message.error("批量登记IP资产失败")}},async submitRegister(){try{if(this.registerForm.assetType==="ip_asset"){await this.submitIpAssetRegister();return}this.registerForm.assetType==="application"?await this.$refs.registerFormRef.validateField(["assetType","hostname","business_system_name","deployed_applications"]):await this.$refs.registerFormRef.validateField(["assetType","hostname","function_purpose","admin1","data_center"]);const{assetType:a,...e}=this.registerForm,n=localStorage.getItem("loginUsername")||"unknown";let c="",l={};a==="device"?(c="/api/add_cmdb_device_management",l={management_ip:e.ip_address,hostname:e.hostname,function_purpose:e.function_purpose,admin1:e.admin1,admin2:e.admin2,data_center:e.data_center,device_type:"自动发现",operation_status:"在用",usernameby:n}):a==="server"?(c="/api/add_cmdb_server_management",l={management_ip:e.ip_address,hostname:e.hostname,function_purpose:e.function_purpose,admin1:e.admin1,admin2:e.admin2,data_center:e.data_center,server_type:"自动发现",operation_status:"在用",usernameby:n}):a==="vm"?(c="/api/add_cmdb_vm_registry",l={management_ip:e.ip_address,hostname:e.hostname,function_purpose:e.function_purpose,admin1:e.admin1,admin2:e.admin2,data_center1:e.data_center,usernameby:n}):a==="application"&&(c="/api/add_cmdb_application_system_info",l={management_ip:e.ip_address,hostname:e.hostname,business_system_name:e.business_system_name,deployed_applications:e.deployed_applications,remarks:"自动发现登记",usernameby:n}),await this.$axios.post(c,l),this.$message.success("资产登记成功"),this.dialogVisible.register=!1,setTimeout(()=>{this.loadData()},1e3)}catch(a){console.error("资产登记失败:",a),this.$message.error("资产登记失败，请检查表单")}}}},X=a=>(G("data-v-b4addc89"),a=a(),J(),a),Z={class:"discovery-results"},$=X(()=>h("div",{class:"page-header"},[h("p",{class:"description",style:{"line-height":"1.5","margin-bottom":"10px",color:"#E6A23C","background-color":"#FDF6EC",padding:"8px 12px","border-radius":"4px","border-left":"4px solid #E6A23C"}}," 查看网络设备自动发现结果，支持导出和资产登记 ")],-1)),ee={class:"button-container"},te={class:"action-bar unified-action-bar"},se={class:"action-bar-left"},ae={class:"action-bar-right"},le={key:0},oe={key:1,class:"text-muted"},ie={key:1,class:"text-muted"},re={key:1,class:"text-muted"},ne={key:1,class:"text-muted"},de={class:"operation-buttons"},ce={class:"pagination-container"},me={class:"dialog-footer"},_e={class:"details-content"},pe={class:"details-content"},ue={key:1,class:"text-muted"},he={class:"dialog-footer"};function ge(a,e,n,c,l,i){const g=d("el-input"),_=d("el-form-item"),v=d("el-col"),p=d("el-option"),k=d("el-select"),I=d("el-row"),f=d("el-button"),P=d("el-form"),F=d("el-card"),T=d("Plus"),C=d("el-icon"),z=d("Back"),B=d("Download"),b=d("el-table-column"),x=d("el-tag"),S=d("el-tooltip"),U=d("el-table"),A=d("el-pagination"),R=d("el-dialog"),w=d("el-descriptions-item"),q=d("el-descriptions"),E=Y("loading");return m(),D("div",Z,[$,t(F,{class:"search-card"},{default:s(()=>[t(P,{inline:!0,class:"search-form"},{default:s(()=>[t(I,{gutter:10},{default:s(()=>[t(v,{xs:24,sm:12,md:6,lg:6},{default:s(()=>[t(_,{label:"IP地址",class:"form-item-with-label"},{default:s(()=>[t(g,{modelValue:l.search.ip_address,"onUpdate:modelValue":e[0]||(e[0]=o=>l.search.ip_address=o),placeholder:"请输入IP地址",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),t(v,{xs:24,sm:12,md:6,lg:6},{default:s(()=>[t(_,{label:"主机名",class:"form-item-with-label"},{default:s(()=>[t(g,{modelValue:l.search.hostname,"onUpdate:modelValue":e[1]||(e[1]=o=>l.search.hostname=o),placeholder:"请输入主机名",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),t(v,{xs:24,sm:12,md:6,lg:6},{default:s(()=>[t(_,{label:"任务ID",class:"form-item-with-label"},{default:s(()=>[t(g,{modelValue:l.search.task_id,"onUpdate:modelValue":e[2]||(e[2]=o=>l.search.task_id=o),placeholder:"请输入任务ID",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),t(v,{xs:24,sm:12,md:6,lg:6},{default:s(()=>[t(_,{label:"登记状态",class:"form-item-with-label"},{default:s(()=>[t(k,{modelValue:l.search.cmdb_status,"onUpdate:modelValue":e[3]||(e[3]=o=>l.search.cmdb_status=o),placeholder:"请选择登记状态",clearable:"",style:{width:"100%"},"popper-class":"cmdb-status-select"},{default:s(()=>[t(p,{label:"已登记",value:"已登记"}),t(p,{label:"未登记",value:"未登记"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(I,{gutter:10},{default:s(()=>[t(v,{xs:24,sm:24,md:24,lg:24,class:"search-buttons-col"},{default:s(()=>[t(_,{label:" ",class:"form-item-with-label search-buttons"},{default:s(()=>[h("div",ee,[t(f,{type:"primary",onClick:i.loadData},{default:s(()=>[r("查询")]),_:1},8,["onClick"]),t(f,{onClick:i.resetSearch},{default:s(()=>[r("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),h("div",te,[h("div",se,[t(f,{type:"success",onClick:i.registerSelected,disabled:l.selectedResults.length===0},{default:s(()=>[t(C,null,{default:s(()=>[t(T)]),_:1}),r(" 登记选中资产 ("+u(l.selectedResults.length)+") ",1)]),_:1},8,["onClick","disabled"]),t(f,{onClick:i.goBack},{default:s(()=>[t(C,null,{default:s(()=>[t(z)]),_:1}),r(" 返回任务列表 ")]),_:1},8,["onClick"])]),h("div",ae,[t(f,{type:"info",onClick:i.exportData},{default:s(()=>[t(C,null,{default:s(()=>[t(B)]),_:1}),r(" 导出数据 ")]),_:1},8,["onClick"])])]),t(F,{class:"table-card"},{default:s(()=>[H((m(),y(U,{data:l.resultList,ref:"table",border:"",stripe:"","table-layout":"fixed",onSortChange:i.handleSortChange,onSelectionChange:i.handleSelectionChange,style:{width:"100%"},"max-height":600,class:"responsive-table"},{default:s(()=>[t(b,{type:"selection",width:"55"}),t(b,{prop:"task_id",label:"任务ID",sortable:"",width:"80"},{default:s(o=>[o.row.task_id?(m(),D("span",le,u(o.row.task_id),1)):(m(),D("span",oe,"-"))]),_:1}),t(b,{prop:"task_name",label:"任务名称",sortable:"","min-width":"150","show-overflow-tooltip":""},{default:s(o=>[o.row.task_name?(m(),y(S,{key:0,content:o.row.task_name,placement:"top","show-after":500},{default:s(()=>[t(x,{type:"info",size:"small"},{default:s(()=>[r(u(o.row.task_name),1)]),_:2},1024)]),_:2},1032,["content"])):(m(),D("span",ie,"未知任务"))]),_:1}),t(b,{prop:"ip_address",label:"IP地址",sortable:"",width:"130","show-overflow-tooltip":""}),t(b,{prop:"hostname",label:"主机名",sortable:"","min-width":"130","show-overflow-tooltip":""}),t(b,{prop:"mac_address",label:"MAC地址",width:"150","show-overflow-tooltip":""}),t(b,{prop:"open_ports",label:"开放端口","min-width":"120","show-overflow-tooltip":""},{default:s(o=>[o.row.open_ports?(m(),y(S,{key:0,content:o.row.open_ports,placement:"top","show-after":500},{default:s(()=>[h("span",null,u(o.row.open_ports),1)]),_:2},1032,["content"])):(m(),D("span",re,"-"))]),_:1}),t(b,{prop:"os_info",label:"操作系统","min-width":"120","show-overflow-tooltip":""},{default:s(o=>[o.row.os_info?(m(),y(S,{key:0,content:o.row.os_info,placement:"top","show-after":500},{default:s(()=>[h("span",null,u(o.row.os_info),1)]),_:2},1032,["content"])):(m(),D("span",ne,"未知"))]),_:1}),t(b,{prop:"discovery_time",label:"发现时间",sortable:"",width:"160","show-overflow-tooltip":""},{default:s(o=>[r(u(i.formatDateTime(o.row.discovery_time)),1)]),_:1}),t(b,{prop:"cmdb_status",label:"登记状态",sortable:"",width:"100"},{default:s(o=>[o.row.cmdb_status==="已登记"?(m(),y(x,{key:0,type:"success",size:"small"},{default:s(()=>[r(" 已登记 ")]),_:1})):(m(),y(x,{key:1,type:"warning",size:"small"},{default:s(()=>[r(" 未登记 ")]),_:1}))]),_:1}),t(b,{label:"操作","min-width":"200",align:"center",fixed:"right"},{default:s(o=>[h("div",de,[t(f,{type:"primary",size:"small",onClick:N=>i.handleRegister(o.row),disabled:o.row.cmdb_status==="已登记"},{default:s(()=>[r(" 登记资产 ")]),_:2},1032,["onClick","disabled"]),t(f,{type:"info",size:"small",onClick:N=>i.handleViewDetails(o.row)},{default:s(()=>[r(" 查看详情 ")]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data","onSortChange","onSelectionChange"])),[[E,l.loading]]),h("div",ce,[t(A,{"current-page":l.search.currentPage,"onUpdate:currentPage":e[4]||(e[4]=o=>l.search.currentPage=o),"page-size":l.search.pageSize,"onUpdate:pageSize":e[5]||(e[5]=o=>l.search.pageSize=o),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:l.search.total,onSizeChange:i.handlePageSizeChange,onCurrentChange:i.handlePageChange,background:""},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),t(R,{modelValue:l.dialogVisible.register,"onUpdate:modelValue":e[9]||(e[9]=o=>l.dialogVisible.register=o),title:"资产登记",width:"600px","destroy-on-close":""},{footer:s(()=>[h("span",me,[t(f,{onClick:e[8]||(e[8]=o=>l.dialogVisible.register=!1)},{default:s(()=>[r("取消")]),_:1}),t(f,{type:"primary",onClick:i.navigateToAssetForm},{default:s(()=>[r("前往登记")]),_:1},8,["onClick"])])]),default:s(()=>[t(P,{model:l.registerForm,ref:"registerFormRef","label-width":"100px",rules:l.registerRules},{default:s(()=>[t(_,{label:"资产类型",prop:"assetType"},{default:s(()=>[l.registerForm.assetType==="ip_asset"?(m(),y(g,{key:0,value:"IP资产管理",disabled:"",style:{width:"100%"}})):(m(),y(k,{key:1,modelValue:l.registerForm.assetType,"onUpdate:modelValue":e[6]||(e[6]=o=>l.registerForm.assetType=o),placeholder:"请选择资产类型",style:{width:"100%"}},{default:s(()=>[t(p,{label:"网络设备",value:"device"}),t(p,{label:"服务器",value:"server"}),t(p,{label:"虚拟机",value:"vm"}),t(p,{label:"应用系统",value:"application"})]),_:1},8,["modelValue"]))]),_:1}),t(_,{label:"IP地址",prop:"ip_address"},{default:s(()=>[t(g,{modelValue:l.registerForm.ip_address,"onUpdate:modelValue":e[7]||(e[7]=o=>l.registerForm.ip_address=o),disabled:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),t(R,{modelValue:l.dialogVisible.details,"onUpdate:modelValue":e[12]||(e[12]=o=>l.dialogVisible.details=o),title:"设备详情",width:"600px","destroy-on-close":"",class:"details-dialog"},{footer:s(()=>[h("span",he,[t(f,{onClick:e[10]||(e[10]=o=>l.dialogVisible.details=!1)},{default:s(()=>[r("关闭")]),_:1}),t(f,{type:"primary",onClick:e[11]||(e[11]=o=>i.handleRegister(l.detailsData)),disabled:l.detailsData.cmdb_status==="已登记"},{default:s(()=>[r(" 登记资产 ")]),_:1},8,["disabled"])])]),default:s(()=>[t(q,{column:1,border:"",size:"small",class:"details-descriptions"},{default:s(()=>[t(w,{label:"IP地址"},{default:s(()=>[r(u(l.detailsData.ip_address),1)]),_:1}),t(w,{label:"主机名"},{default:s(()=>[r(u(l.detailsData.hostname),1)]),_:1}),t(w,{label:"MAC地址"},{default:s(()=>[r(u(l.detailsData.mac_address||"未知"),1)]),_:1}),t(w,{label:"开放端口"},{default:s(()=>[h("div",_e,u(l.detailsData.open_ports||"无"),1)]),_:1}),t(w,{label:"操作系统"},{default:s(()=>[h("div",pe,u(l.detailsData.os_info||"未知"),1)]),_:1}),t(w,{label:"发现时间"},{default:s(()=>[r(u(i.formatDateTime(l.detailsData.discovery_time)),1)]),_:1}),t(w,{label:"登记状态"},{default:s(()=>[l.detailsData.cmdb_status==="已登记"?(m(),y(x,{key:0,type:"success",size:"small"},{default:s(()=>[r(" 已登记 ")]),_:1})):(m(),y(x,{key:1,type:"warning",size:"small"},{default:s(()=>[r(" 未登记 ")]),_:1}))]),_:1}),t(w,{label:"任务名称"},{default:s(()=>[l.detailsData.task_name?(m(),y(x,{key:0,type:"info",size:"small"},{default:s(()=>[r(u(l.detailsData.task_name),1)]),_:1})):(m(),D("span",ue,"未知任务"))]),_:1}),t(w,{label:"任务ID"},{default:s(()=>[r(u(l.detailsData.task_id||"-"),1)]),_:1})]),_:1})]),_:1},8,["modelValue"])])}const ye=L(W,[["render",ge],["__scopeId","data-v-b4addc89"]]);export{ye as default};
