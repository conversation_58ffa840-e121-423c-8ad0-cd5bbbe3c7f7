import{_ as B,z as E,v as N,A as q,c as O,a as e,f as i,w as s,b as d,h as m,B as F,C as T,m as j,x as G,p as H,q as J,e as C}from"./index-Bo9zKuAo.js";import{u as D,w as K,F as L}from"./FileSaver.min-BlIK9FxR.js";const M={components:{Plus:q,Search:N,Download:E},data(){var a,t,u;return{userArr:[],loading:!1,hasDeletePermission:(a=localStorage.getItem("role_code"))==null?void 0:a.includes("D"),hasUpdatePermission:(t=localStorage.getItem("role_code"))==null?void 0:t.includes("U"),hasInsertPermission:(u=localStorage.getItem("role_code"))==null?void 0:u.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{dict_code:"",keyword:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,dict_type:"",dict_type_name:"",dict_code:"",dict_name:"",created_at:""}}},mounted(){this.loadData()},methods:{handlePageChange(a){this.search.currentPage=a,this.loadData()},handlePageSizeChange(a){this.search.pageSize=parseInt(a),this.search.currentPage=1,this.loadData()},handleSortChange({prop:a,order:t}){this.search.sortProp=a,this.search.sortOrder=t==="ascending"?"asc":"desc",this.loadData()},resetSearch(){this.search={dict_code:"",keyword:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},async loadData(){try{this.loading=!0;const a=await this.$axios.post("/api/get_cmdb_data_dictionary",this.search);this.userArr=a.data.msg,this.search.total=a.data.total}catch(a){console.error("数据加载失败:",a),this.$message.error("数据加载失败")}finally{this.loading=!1}},async submitAdd(){try{if(!this.formData.dict_type||!this.formData.dict_type_name||!this.formData.dict_code||!this.formData.dict_name){this.$message.warning("请填写所有必填字段");return}const a={...this.formData,usernameby:localStorage.getItem("loginUsername")||"unknown"};delete a.id;const t=await this.$axios.post("/api/add_cmdb_data_dictionary",a);this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(a){console.error("添加失败:",a),a.response&&a.response.data&&a.response.data.msg?this.$message.error(a.response.data.msg):this.$message.error("添加失败")}},async submitEdit(){try{if(!this.formData.dict_type||!this.formData.dict_type_name||!this.formData.dict_code||!this.formData.dict_name){this.$message.warning("请填写所有必填字段");return}if(!this.formData.id){this.$message.error("缺少记录ID，无法更新");return}const a={...this.formData,usernameby:localStorage.getItem("loginUsername")||"unknown"},t=await this.$axios.post("/api/update_cmdb_data_dictionary",a);this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(a){console.error("更新失败:",a),a.response&&a.response.data&&a.response.data.msg?this.$message.error(a.response.data.msg):this.$message.error("更新失败")}},async submitDelete(){try{await this.$axios.post("/api/del_cmdb_data_dictionary",this.formData),this.$message.success("删除成功"),this.loadData(),this.dialogVisible.delete=!1}catch(a){console.error("删除失败:",a),this.$message.error("删除失败")}},handleAdd(){this.dialogVisible.add=!this.dialogVisible.add,this.formData={}},handleEdit(a,t){this.dialogVisible.edit=!0,this.formData.id=t.id,this.formData.dict_code=t.dict_code,this.formData.dict_name=t.dict_name,this.formData.dict_type=t.dict_type,this.formData.dict_type_name=t.dict_type_name},handleDelete(a,t){this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=t.id,this.formData.dict_code=t.dict_code},exportData(){const t=this.$refs.table.columns,u=t.map(f=>f.label),V=this.userArr.map(f=>t.map(b=>f[b.property])),l=[u,...V],n=D.aoa_to_sheet(l),c=D.book_new();D.book_append_sheet(c,n,"Sheet1");const r=K(c,{bookType:"xlsx",type:"array"}),h=new Blob([r],{type:"application/octet-stream"});L.saveAs(h,"数据字典.xlsx")}}},p=a=>(H("data-v-446a40a3"),a=a(),J(),a),Q={class:"user-manage"},R={class:"dialogdiv"},W=p(()=>i("span",{class:"label"},"字典类型:",-1)),X=p(()=>i("span",{class:"label"},"字典类型名称:",-1)),Y=p(()=>i("span",{class:"label"},"字典代码:",-1)),Z=p(()=>i("span",{class:"label"},"字典名称:",-1)),$={class:"dialog-footer"},ee={class:"dialogdiv"},te=p(()=>i("span",{class:"label"},"字典类型:",-1)),ae=p(()=>i("span",{class:"label"},"字典类型名称:",-1)),le=p(()=>i("span",{class:"label"},"字典代码:",-1)),oe=p(()=>i("span",{class:"label"},"字典名称:",-1)),se={class:"dialog-footer"},ie={class:"button-container"},de={class:"action-bar unified-action-bar"},ne={class:"action-bar-left"},re={class:"action-bar-right"},ce={style:{display:"flex","white-space":"nowrap"}},me={class:"pagination"};function _e(a,t,u,V,l,n){const c=d("el-input"),r=d("el-button"),h=d("el-dialog"),f=d("el-alert"),b=d("el-form-item"),g=d("el-col"),k=d("Search"),y=d("el-icon"),x=d("el-row"),v=d("el-form"),w=d("el-card"),S=d("Plus"),P=d("Download"),_=d("el-table-column"),U=d("el-table"),z=d("el-pagination"),I=T("loading");return C(),O("div",Q,[e(h,{modelValue:l.dialogVisible.add,"onUpdate:modelValue":t[5]||(t[5]=o=>l.dialogVisible.add=o),title:"添加数据字典",width:"400","align-center":""},{footer:s(()=>[i("div",$,[e(r,{onClick:t[4]||(t[4]=o=>l.dialogVisible.add=!1)},{default:s(()=>[m("返回")]),_:1}),e(r,{type:"primary",onClick:n.submitAdd},{default:s(()=>[m("确定")]),_:1},8,["onClick"])])]),default:s(()=>[i("div",R,[i("p",null,[W,e(c,{modelValue:l.formData.dict_type,"onUpdate:modelValue":t[0]||(t[0]=o=>l.formData.dict_type=o),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),i("p",null,[X,e(c,{modelValue:l.formData.dict_type_name,"onUpdate:modelValue":t[1]||(t[1]=o=>l.formData.dict_type_name=o),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),i("p",null,[Y,e(c,{modelValue:l.formData.dict_code,"onUpdate:modelValue":t[2]||(t[2]=o=>l.formData.dict_code=o),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),i("p",null,[Z,e(c,{modelValue:l.formData.dict_name,"onUpdate:modelValue":t[3]||(t[3]=o=>l.formData.dict_name=o),style:{width:"240px"},clearable:""},null,8,["modelValue"])])])]),_:1},8,["modelValue"]),e(h,{modelValue:l.dialogVisible.edit,"onUpdate:modelValue":t[11]||(t[11]=o=>l.dialogVisible.edit=o),title:"编辑监控IP",width:"400","align-center":""},{footer:s(()=>[i("div",se,[e(r,{onClick:t[10]||(t[10]=o=>l.dialogVisible.edit=!1)},{default:s(()=>[m("取消")]),_:1}),e(r,{type:"primary",onClick:n.submitEdit},{default:s(()=>[m("更新")]),_:1},8,["onClick"])])]),default:s(()=>[i("div",ee,[i("p",null,[te,e(c,{modelValue:l.formData.dict_type,"onUpdate:modelValue":t[6]||(t[6]=o=>l.formData.dict_type=o),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),i("p",null,[ae,e(c,{modelValue:l.formData.dict_type_name,"onUpdate:modelValue":t[7]||(t[7]=o=>l.formData.dict_type_name=o),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),i("p",null,[le,e(c,{modelValue:l.formData.dict_code,"onUpdate:modelValue":t[8]||(t[8]=o=>l.formData.dict_code=o),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),i("p",null,[oe,e(c,{modelValue:l.formData.dict_name,"onUpdate:modelValue":t[9]||(t[9]=o=>l.formData.dict_name=o),style:{width:"240px"},clearable:""},null,8,["modelValue"])])])]),_:1},8,["modelValue"]),e(h,{modelValue:l.dialogVisible.delete,"onUpdate:modelValue":t[13]||(t[13]=o=>l.dialogVisible.delete=o),title:"删除数据字典",width:"500","align-center":""},{footer:s(()=>[i("div",null,[e(r,{onClick:t[12]||(t[12]=o=>l.dialogVisible.delete=!1)},{default:s(()=>[m("取消")]),_:1}),e(r,{type:"danger",onClick:n.submitDelete},{default:s(()=>[m("确认删除")]),_:1},8,["onClick"])])]),default:s(()=>[e(f,{type:"warning",title:`确定要删除 数据字典代码 为 ${l.formData.dict_code} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),e(w,{class:"search-card"},{default:s(()=>[e(v,{inline:!0},{default:s(()=>[e(x,{gutter:20},{default:s(()=>[e(g,{xs:24,sm:12,md:6,lg:6},{default:s(()=>[e(b,{label:"字典代码",class:"form-item-with-label"},{default:s(()=>[e(c,{modelValue:l.search.dict_code,"onUpdate:modelValue":t[14]||(t[14]=o=>l.search.dict_code=o),placeholder:"请输入字典代码",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(g,{xs:24,sm:12,md:6,lg:6},{default:s(()=>[e(b,{label:"关键词搜索",class:"form-item-with-label"},{default:s(()=>[e(c,{modelValue:l.search.keyword,"onUpdate:modelValue":t[15]||(t[15]=o=>l.search.keyword=o),placeholder:"字典类型名称/字典名称",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(g,{xs:24,sm:12,md:12,lg:12,class:"search-buttons-col"},{default:s(()=>[e(b,{label:" ",class:"form-item-with-label search-buttons"},{default:s(()=>[i("div",ie,[e(r,{type:"primary",onClick:n.loadData},{default:s(()=>[e(y,null,{default:s(()=>[e(k)]),_:1}),m("查询 ")]),_:1},8,["onClick"]),e(r,{onClick:n.resetSearch},{default:s(()=>[m("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),i("div",de,[i("div",ne,[e(r,{type:"success",disabled:!l.hasInsertPermission,onClick:n.handleAdd},{default:s(()=>[e(y,null,{default:s(()=>[e(S)]),_:1}),m("新增 ")]),_:1},8,["disabled","onClick"])]),i("div",re,[e(r,{type:"info",onClick:n.exportData},{default:s(()=>[e(y,null,{default:s(()=>[e(P)]),_:1}),m(" 导出数据 ")]),_:1},8,["onClick"])])]),e(w,{class:"table-card"},{default:s(()=>[F((C(),j(U,{data:l.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:n.handleSortChange},{default:s(()=>[G("",!0),e(_,{prop:"dict_type",label:"字典类型",sortable:""}),e(_,{prop:"dict_type_name",label:"字典类型名称",sortable:""}),e(_,{prop:"dict_code",label:"字典代码",sortable:""}),e(_,{prop:"dict_name",label:"字典名称",sortable:""}),e(_,{prop:"created_at",label:"创建时间",sortable:""}),e(_,{prop:"created_by",label:"创建人",sortable:""}),e(_,{prop:"updated_at",label:"更新时间",sortable:""}),e(_,{prop:"updated_by",label:"更新人",sortable:""}),e(_,{label:"操作",fixed:"right"},{default:s(o=>[i("div",ce,[e(r,{size:"small",type:"warning",disabled:!l.hasUpdatePermission,onClick:A=>n.handleEdit(o.$index,o.row)},{default:s(()=>[m("编辑")]),_:2},1032,["disabled","onClick"]),e(r,{size:"small",type:"danger",disabled:!l.hasDeletePermission,onClick:A=>n.handleDelete(o.$index,o.row)},{default:s(()=>[m("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[I,l.loading]]),i("div",me,[e(z,{background:"","current-page":l.search.currentPage,"page-size":l.search.pageSize,total:l.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:n.handlePageSizeChange,onCurrentChange:n.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const he=B(M,[["render",_e],["__scopeId","data-v-446a40a3"]]);export{he as default};
