import{N as x,_ as ue,S as _e,T as fe,U as me,v as ge,r as k,J as ve,K as X,V as ye,o as he,E as m,L as pe,c as P,a as t,w as i,m as O,x as Y,b as y,F as Q,f as C,B as ke,h as L,t as Z,C as be,p as we,q as Se,e as A}from"./index-C0WPPkX4.js";async function je(r){var n,f;try{console.log("发送调度任务列表请求:",r),console.log("请求URL:","/api/discovery/schedule/get_schedule_tasks");const e=await x.post("/api/discovery/schedule/get_schedule_tasks",r,{timeout:3e4});return console.log("原始响应:",e),e&&e.data?e.data:(console.error("响应格式不正确:",e),{code:1,msg:"响应格式不正确"})}catch(e){console.error("调度任务列表请求失败:",e),console.error("错误详情:",e.response||e.message||e);try{console.log("尝试使用备用URL...");const l=await x.post("/discovery/schedule/get_schedule_tasks",r,{timeout:3e4});if(console.log("备用URL响应:",l),l&&l.data)return l.data}catch(l){console.error("备用URL也失败:",l)}return{code:1,msg:((f=(n=e.response)==null?void 0:n.data)==null?void 0:f.msg)||e.message||"获取调度任务列表失败"}}}async function Be(r){var n,f;try{console.log("发送获取调度任务详情请求:",r),console.log("请求URL:","/api/discovery/schedule/get_schedule_task_detail");const e=await x.post("/api/discovery/schedule/get_schedule_task_detail",r);return console.log("获取调度任务详情响应:",e),e&&e.data?e.data:(console.error("响应格式不正确:",e),{code:1,msg:"响应格式不正确"})}catch(e){return console.error("获取调度任务详情请求失败:",e),console.error("错误详情:",e.response||e.message||e),e.response&&e.response.data?e.response.data:{code:1,msg:((f=(n=e.response)==null?void 0:n.data)==null?void 0:f.msg)||e.message||"获取调度任务详情失败"}}}async function Te(r){var n,f;try{console.log("发送添加调度任务请求:",r),console.log("请求URL:","/api/discovery/schedule/add_schedule_task");const e=await x.post("/api/discovery/schedule/add_schedule_task",r);return console.log("添加调度任务响应:",e),e&&e.data?e.data:(console.error("响应格式不正确:",e),{code:1,msg:"响应格式不正确"})}catch(e){return console.error("添加调度任务请求失败:",e),console.error("错误详情:",e.response||e.message||e),e.response&&e.response.data?e.response.data:{code:1,msg:((f=(n=e.response)==null?void 0:n.data)==null?void 0:f.msg)||e.message||"添加调度任务失败"}}}async function De(r){var n,f;try{console.log("发送更新调度任务请求:",r),console.log("请求URL:","/api/discovery/schedule/update_schedule_task");const e=await x.post("/api/discovery/schedule/update_schedule_task",r);return console.log("更新调度任务响应:",e),e&&e.data?e.data:(console.error("响应格式不正确:",e),{code:1,msg:"响应格式不正确"})}catch(e){return console.error("更新调度任务请求失败:",e),console.error("错误详情:",e.response||e.message||e),e.response&&e.response.data?e.response.data:{code:1,msg:((f=(n=e.response)==null?void 0:n.data)==null?void 0:f.msg)||e.message||"更新调度任务失败"}}}async function Pe(r){var n,f;try{console.log("发送删除调度任务请求:",r),console.log("请求URL:","/api/discovery/schedule/del_schedule_task");const e=await x.post("/api/discovery/schedule/del_schedule_task",r);return console.log("删除调度任务响应:",e),e&&e.data?e.data:(console.error("响应格式不正确:",e),{code:1,msg:"响应格式不正确"})}catch(e){return console.error("删除调度任务请求失败:",e),console.error("错误详情:",e.response||e.message||e),e.response&&e.response.data?e.response.data:{code:1,msg:((f=(n=e.response)==null?void 0:n.data)==null?void 0:f.msg)||e.message||"删除调度任务失败"}}}async function Ne(r){var n,f;try{console.log("发送更新调度任务状态请求:",r),console.log("请求URL:","/api/discovery/schedule/update_schedule_task_status");const e=await x.post("/api/discovery/schedule/update_schedule_task_status",r);return console.log("更新调度任务状态响应:",e),e&&e.data?e.data:(console.error("响应格式不正确:",e),{code:1,msg:"响应格式不正确"})}catch(e){return console.error("更新调度任务状态请求失败:",e),console.error("错误详情:",e.response||e.message||e),e.response&&e.response.data?e.response.data:{code:1,msg:((f=(n=e.response)==null?void 0:n.data)==null?void 0:f.msg)||e.message||"更新调度任务状态失败"}}}async function qe(r){var n,f;try{console.log("发送执行调度任务请求:",r),console.log("请求URL:","/api/discovery/schedule/run_schedule_task");const e=await x.post("/api/discovery/schedule/run_schedule_task",r);return console.log("执行调度任务响应:",e),e&&e.data?e.data:(console.error("响应格式不正确:",e),{code:1,msg:"响应格式不正确"})}catch(e){return console.error("执行调度任务请求失败:",e),console.error("错误详情:",e.response||e.message||e),e.response&&e.response.data?e.response.data:{code:1,msg:((f=(n=e.response)==null?void 0:n.data)==null?void 0:f.msg)||e.message||"执行调度任务失败"}}}const Ce={name:"ScheduleTaskForm",components:{Search:ge,Delete:me,Select:fe,InfoFilled:_e},props:{formData:{type:Object,default:()=>({})},mode:{type:String,default:"add"}},emits:["submit","cancel"],setup(r,{emit:n}){const f=k(null),e=k(null),l=ve({id:void 0,task_name:"",description:"",schedule_type:"manual",schedule_value:"",status:"inactive",discovery_task_ids:[]}),W={task_name:[{required:!0,message:"请输入任务名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],schedule_type:[{required:!0,message:"请选择调度类型",trigger:"change"}],schedule_value:[{required:!0,message:"请输入调度值",trigger:"change",validator:(s,o,a)=>{l.schedule_type==="manual"||o?a():a(new Error("请输入调度值"))}}],status:[{required:!0,message:"请选择状态",trigger:"change"}]},I=k(""),b=k(""),g=k("1"),D=k(""),c=k([]),w=k(""),S=k(!1),U=k(1),E=k(10),T=k(0);k([]);const R=k([]),q=X(()=>{if(!c.value)return console.warn("可用任务列表为空:",c.value),[];if(!Array.isArray(c.value))return console.warn("可用任务列表不是数组:",c.value),[];try{const s=c.value.filter(_=>_&&typeof _=="object"&&_.id);if(s.length===0)return[];const o=[...s].sort((_,p)=>{const d=l.discovery_task_ids.includes(_.id),V=l.discovery_task_ids.includes(p.id);return d&&!V?-1:!d&&V?1:0}),a=(U.value-1)*E.value,v=a+E.value;return o.slice(a,v)}catch(s){return console.error("过滤任务列表时出错:",s),[]}}),B=X(()=>{if(!c.value)return console.warn("计算已选任务时，可用任务列表为空:",c.value),[];if(!Array.isArray(c.value))return console.warn("计算已选任务时，可用任务列表不是数组:",c.value),[];try{return c.value.filter(s=>s&&typeof s=="object"&&s.id&&l.discovery_task_ids.includes(s.id))}catch(s){return console.error("计算已选任务时出错:",s),[]}}),z=()=>{r.formData&&(console.log("初始化表单数据:",r.formData),Object.keys(l).forEach(s=>{r.formData[s]!==void 0&&(l[s]=r.formData[s])}),r.formData.discovery_task_ids?Array.isArray(r.formData.discovery_task_ids)?(l.discovery_task_ids=[...r.formData.discovery_task_ids],console.log("设置已选任务IDs:",l.discovery_task_ids)):(console.warn("提供的discovery_task_ids不是数组:",r.formData.discovery_task_ids),l.discovery_task_ids=[]):l.discovery_task_ids=[],K())},K=()=>{const s=l.schedule_type,o=l.schedule_value||"";if(s==="once")I.value=o||"";else if(s==="daily")b.value=o||"";else if(s==="weekly")if(o){const a=o.split(",");a.length===2?(g.value=a[0],D.value=a[1]):(g.value="1",D.value="")}else g.value="1",D.value=""},J=s=>{if(l.schedule_value="",s==="once"){const o=new Date;o.setDate(o.getDate()+1),I.value=G(o),M()}else s==="daily"?(b.value="03:00:00",M()):s==="weekly"&&(g.value="1",D.value="03:00:00",M())},M=()=>{const s=l.schedule_type;s==="once"?l.schedule_value=I.value||"":s==="daily"?l.schedule_value=b.value||"":s==="weekly"&&(g.value&&D.value?l.schedule_value=`${g.value},${D.value}`:l.schedule_value="")},G=s=>{const o=H=>String(H).padStart(2,"0"),a=s.getFullYear(),v=o(s.getMonth()+1),_=o(s.getDate()),p=o(s.getHours()),d=o(s.getMinutes()),V=o(s.getSeconds());return`${a}-${v}-${_} ${p}:${d}:${V}`},u=async()=>{console.log("开始获取可用发现任务..."),S.value=!0,c.value=[],T.value=0,console.log("当前模式:",(r==null?void 0:r.mode)||"unknown"),console.log("当前表单数据:",l);try{console.log("开始获取发现任务列表..."),console.log("搜索关键词:",w.value);const s=/^\d+$/.test(w.value);console.log("是否按ID搜索:",s);const o=await x.post("/api/discovery/get_discovery_tasks",{task_name:w.value,status:"",task_type:"",currentPage:1,pageSize:1e3,sortProp:"created_at",sortOrder:"desc"},{timeout:3e4});if(!o||!o.data){console.error("获取发现任务列表失败: 无效的响应"),m.error("获取发现任务列表失败: 无效的响应"),S.value=!1;return}if(o.data.code!==0){console.error("获取发现任务列表失败:",o.data.msg),m.error(o.data.msg||"获取发现任务列表失败"),S.value=!1;return}if(!Array.isArray(o.data.msg)){console.error("获取发现任务列表失败: 响应数据不是数组"),m.error("获取发现任务列表失败: 响应数据格式错误"),S.value=!1;return}console.log("获取到发现任务列表，原始数量:",o.data.msg.length);const a=o.data.msg.filter(v=>v&&typeof v=="object"&&v.id);if(console.log("有效任务数量:",a.length),a.length===0){console.warn("没有有效的发现任务"),m.warning("没有找到有效的发现任务"),S.value=!1;return}try{console.log("开始获取任务调度信息...");const v=await x.post("/api/discovery/schedule/get_task_schedule_info",{task_ids:a.map(d=>d.id)},{timeout:3e4});let _={};v&&v.data&&v.data.code===0?(_=v.data.msg||{},console.log("获取任务调度信息成功")):console.warn("获取任务调度信息失败，使用空对象");const p=a.map(d=>{const V=l.discovery_task_ids.includes(d.id)||_[d.id]&&_[d.id]===l.id;_[d.id]&&_[d.id]===l.id&&!l.discovery_task_ids.includes(d.id)&&(l.discovery_task_ids.push(d.id),console.log(`添加已关联但未选中的任务 ${d.id} 到已选列表`));const H=_[d.id]&&_[d.id]!==l.id?1:0;return{id:d.id,task_name:d.task_name||`任务 ${d.id}`,task_type:d.task_type||"未知",schedule_task_id:_[d.id]||null,schedule_task_name:_[d.id]?_[`${d.id}_name`]:null,selected:V,status:H}});if(c.value=p,console.log("处理后的任务数量:",p.length),console.log("处理后的任务列表:",JSON.stringify(p.slice(0,3))),T.value=p.length,R.value=p.filter(d=>d.schedule_task_id&&d.schedule_task_id!==l.id),console.log("已关联其他调度任务的任务数量:",R.value.length),r.mode==="edit"&&l.discovery_task_ids.length>0){U.value=1,console.log("编辑模式，重置到第一页以显示已选任务"),console.log("编辑模式下的已选任务IDs:",l.discovery_task_ids);const d=p.map(H=>H.id),V=l.discovery_task_ids.filter(H=>!d.includes(H));V.length>0&&(console.warn("有一些已选任务不在可用任务列表中:",V),m.warning(`有 ${V.length} 个已选任务不在可用任务列表中`))}j()}catch(v){console.error("获取任务调度信息失败:",v);const _=a.map(p=>({id:p.id,task_name:p.task_name||`任务 ${p.id}`,task_type:p.task_type||"未知",schedule_task_id:null,schedule_task_name:null,selected:l.discovery_task_ids.includes(p.id),status:0}));c.value=_,T.value=_.length,R.value=[],r.mode==="edit"&&l.discovery_task_ids.length>0&&(U.value=1),j(),m.warning("获取任务调度信息失败，部分功能可能受到影响")}}catch(s){console.error("获取发现任务列表失败:",s),s.message&&s.message.includes("column reference")?(m.warning("没有找到匹配的发现任务，请修改搜索条件"),w.value="",c.value=[],T.value=0):(console.error("获取发现任务列表失败详细信息:",s),m.error("获取发现任务列表失败，请稍后重试"),c.value=[{id:1,task_name:"测试任务1",task_type:"网络扫描",schedule_task_id:null,selected:!1},{id:2,task_name:"测试任务2",task_type:"网络扫描",schedule_task_id:null,selected:!1},{id:3,task_name:"测试任务3",task_type:"网络扫描",schedule_task_id:null,selected:!1}],T.value=3)}finally{S.value=!1}},j=()=>{console.log("开始设置表格选中状态..."),c.value&&Array.isArray(c.value)&&(c.value.forEach(s=>{s&&s.id&&(s.selected=l.discovery_task_ids.includes(s.id))}),console.log("已更新可用任务的选中状态")),setTimeout(()=>{try{if(!e.value){console.warn("表格引用不存在，无法设置选中状态");return}const s=e.value.$el;if(!s||!document.body.contains(s)){console.warn("表格 DOM 元素不存在或已从文档中移除，无法设置选中状态");return}if(console.log("开始设置表格选中状态，已选任务IDs:",l.discovery_task_ids),typeof e.value.clearSelection=="function")e.value.clearSelection();else{console.warn("表格的 clearSelection 方法不存在");return}l.discovery_task_ids&&l.discovery_task_ids.length>0&&c.value&&Array.isArray(c.value)?(c.value.forEach(o=>{(l.discovery_task_ids.includes(o.id)||o.schedule_task_id===l.id)&&h(o)&&typeof e.value.toggleRowSelection=="function"&&e.value.toggleRowSelection(o,!0)}),console.log("表格选中状态已设置，已选择",l.discovery_task_ids.length,"个任务")):console.log("没有需要选中的任务")}catch(s){console.error("设置表格选中状态时出错:",s)}},100)},h=s=>{const o=!s.schedule_task_id||s.schedule_task_id===l.id;return s.schedule_task_id===l.id&&!l.discovery_task_ids.includes(s.id)&&(l.discovery_task_ids.push(s.id),console.log(`添加已关联但未选中的任务 ${s.id} 到已选列表`)),console.log(`任务 ${s.id} ${s.task_name} 是否可选: ${o}`),o},$=(s,o)=>{if(console.log(`手动${o?"选中":"取消选中"}任务:`,s),s.selected=o,o)l.discovery_task_ids.includes(s.id)||l.discovery_task_ids.push(s.id);else{const a=l.discovery_task_ids.indexOf(s.id);a!==-1&&l.discovery_task_ids.splice(a,1)}console.log("更新后的已选任务IDs:",l.discovery_task_ids)},ee=s=>{E.value=s,U.value=1},se=s=>{U.value=s},le=async()=>{try{if(!w.value||w.value.trim()===""){await u();return}S.value=!0,console.log("搜索发现任务，关键词:",w.value);try{await u()}catch(s){if(s.message&&s.message.includes("column reference"))m.warning("没有找到匹配的发现任务，请修改搜索条件"),w.value="",await u();else throw s}}catch(s){console.error("搜索发现任务失败:",s),m.error("搜索发现任务失败，请稍后重试")}finally{S.value=!1}},oe=()=>{if(!e.value){console.warn("表格引用不存在，无法全选任务");return}const s=e.value.$el;if(!s||!document.body.contains(s)){console.warn("表格 DOM 元素不存在或已从文档中移除，无法全选任务");return}if(!c.value||!Array.isArray(c.value)){console.warn("可用任务列表不是数组，无法全选任务");return}const o=c.value.filter(a=>a&&h(a));if(o.length===0){m.warning("没有可选的任务");return}try{if(typeof e.value.clearSelection=="function")e.value.clearSelection();else{console.warn("表格的 clearSelection 方法不存在");return}o.forEach(a=>{if(typeof e.value.toggleRowSelection=="function"&&e.value.toggleRowSelection(a,!0),l.discovery_task_ids.includes(a.id)||l.discovery_task_ids.push(a.id),c.value&&Array.isArray(c.value)){const v=c.value.findIndex(_=>_&&_.id===a.id);v!==-1&&(c.value[v].selected=!0)}}),m.success(`已选择 ${o.length} 个任务`)}catch(a){console.error("全选任务时出错:",a),m.error("全选任务失败，请重试")}},ae=()=>{if(!e.value){console.warn("表格引用不存在，无法清空选择");return}const s=e.value.$el;if(!s||!document.body.contains(s)){console.warn("表格 DOM 元素不存在或已从文档中移除，无法清空选择");return}try{typeof e.value.clearSelection=="function"?e.value.clearSelection():console.warn("表格的 clearSelection 方法不存在"),l.discovery_task_ids=[],c.value&&Array.isArray(c.value)&&c.value.forEach(o=>{o&&(o.selected=!1)}),m.success("已清空所有选择")}catch(o){console.error("清空选择时出错:",o),m.error("清空选择失败，请重试")}},te=s=>{if(console.log("表格选择变化:",s),F.value){console.log("组件已卸载，忽略表格选择变化");return}if(!Array.isArray(s)){console.warn("表格选择变化收到非数组数据:",s);return}try{const o=s.filter(a=>a&&typeof a=="object"&&a.id);l.discovery_task_ids=o.map(a=>a.id),console.log("更新后的已选任务IDs:",l.discovery_task_ids),c.value&&Array.isArray(c.value)&&c.value.forEach(a=>{a&&a.id&&(a.selected=l.discovery_task_ids.includes(a.id))})}catch(o){console.error("处理表格选择变化时出错:",o)}},re=s=>l.discovery_task_ids.includes(s),ne=s=>{const o=l.discovery_task_ids.indexOf(s.id);o===-1?l.discovery_task_ids.push(s.id):l.discovery_task_ids.splice(o,1)},ce=s=>{const o=l.discovery_task_ids.indexOf(s.id);o!==-1&&l.discovery_task_ids.splice(o,1)},de=async()=>{console.log("开始提交表单，当前表单数据:",l),console.log("当前已选任务IDs:",l.discovery_task_ids);let s=!0;if(l.task_name||(m.error("调度任务名称不能为空"),s=!1),l.schedule_type||(m.error("调度类型不能为空"),s=!1),l.schedule_type!=="manual"&&!l.schedule_value&&(m.error("调度值不能为空"),s=!1),(!l.discovery_task_ids||l.discovery_task_ids.length===0)&&(m.error("请选择至少一个发现任务"),s=!1),!s){console.warn("手动验证失败");return}try{const o={...l};console.log("提交表单数据:",o);let a;r.mode==="add"?a=await Te(o):a=await De(o),console.log("提交响应:",a),a.code===0?(m.success(r.mode==="add"?"添加成功":"更新成功"),n("submit",a.data)):(console.error("提交失败:",a.msg),m.error(a.msg||(r.mode==="add"?"添加失败":"更新失败")))}catch(o){console.error(r.mode==="add"?"添加调度任务失败:":"更新调度任务失败:",o);let a=r.mode==="add"?"添加调度任务失败":"更新调度任务失败";o.response&&o.response.data&&o.response.data.msg?a=o.response.data.msg:o.message&&(a=o.message),m.error(a)}},ie=()=>{n("cancel")};ye(()=>r.formData,()=>{z()},{deep:!0});const F=k(!1);return he(()=>{console.log("组件挂载完成"),F.value=!1,setTimeout(async()=>{if(F.value){console.log("组件已卸载，取消初始化操作");return}console.log("初始化表单数据"),z(),setTimeout(async()=>{if(F.value){console.log("组件已卸载，取消获取可用任务");return}console.log("开始获取可用任务");try{await u(),console.log("获取可用任务成功")}catch(s){console.error("获取可用任务失败:",s),F.value||m.error("获取可用任务失败，请刷新页面重试")}},100)},100)}),pe(()=>{console.log("组件即将卸载"),F.value=!0}),{formRef:f,taskTableRef:e,form:l,rules:W,onceDateTime:I,dailyTime:b,weeklyDay:g,weeklyTime:D,availableTasks:c,selectedTasks:B,searchKeyword:w,tasksLoading:S,currentPage:U,pageSize:E,totalTasks:T,alreadyAssignedTasks:R,filteredTasks:q,handleScheduleTypeChange:J,updateScheduleValue:M,getAvailableTasks:u,searchDiscoveryTasks:le,isTaskSelected:re,toggleTaskSelection:ne,removeSelectedTask:ce,isTaskSelectable:h,handleManualSelect:$,handleSelectionChange:te,handleSizeChange:ee,handleCurrentChange:se,selectAllAvailableTasks:oe,clearAllSelection:ae,submitForm:de,cancel:ie}}},N=r=>(we("data-v-3b42c9a4"),r=r(),Se(),r),Ve={class:"schedule-task-form"},Ae=N(()=>C("div",{class:"form-help-text"},"选择任务将在什么时间执行一次",-1)),xe=N(()=>C("div",{class:"form-help-text"},"选择任务每天在什么时间执行",-1)),Ie={class:"weekly-schedule-container"},Ue=N(()=>C("div",{class:"form-help-text"},"选择任务每周的哪一天和什么时间执行",-1)),Re={class:"discovery-tasks-selector"},ze={class:"discovery-tasks-header"},Ee={class:"search-and-actions"},He={class:"action-buttons"},Le={class:"selection-info"},Me=N(()=>C("div",{class:"assigned-tasks-info"}," 已被本调度任务关联的任务会自动显示为已选中状态。如需取消关联，请取消勾选相应任务。 ",-1)),Fe={class:"table-pagination"};function Oe(r,n,f,e,l,W){const I=y("el-input"),b=y("el-form-item"),g=y("el-option"),D=y("el-select"),c=y("el-date-picker"),w=y("el-alert"),S=y("el-time-picker"),U=y("Search"),E=y("el-icon"),T=y("el-button"),R=y("el-tag"),q=y("InfoFilled"),B=y("el-tooltip"),z=y("el-table-column"),K=y("el-table"),J=y("el-pagination"),M=y("el-form"),G=be("loading");return A(),P("div",Ve,[t(M,{ref:"formRef",model:e.form,rules:e.rules,"label-width":"120px",size:"default"},{default:i(()=>[t(b,{label:"任务名称",prop:"task_name"},{default:i(()=>[t(I,{modelValue:e.form.task_name,"onUpdate:modelValue":n[0]||(n[0]=u=>e.form.task_name=u),placeholder:"请输入调度任务名称"},null,8,["modelValue"])]),_:1}),t(b,{label:"任务描述",prop:"description"},{default:i(()=>[t(I,{modelValue:e.form.description,"onUpdate:modelValue":n[1]||(n[1]=u=>e.form.description=u),type:"textarea",rows:3,placeholder:"请输入任务描述"},null,8,["modelValue"])]),_:1}),t(b,{label:"调度类型",prop:"schedule_type"},{default:i(()=>[t(D,{modelValue:e.form.schedule_type,"onUpdate:modelValue":n[2]||(n[2]=u=>e.form.schedule_type=u),placeholder:"请选择调度类型",style:{width:"100%"},onChange:e.handleScheduleTypeChange},{default:i(()=>[t(g,{label:"手动",value:"manual"}),t(g,{label:"一次性",value:"once"}),t(g,{label:"每日",value:"daily"}),t(g,{label:"每周",value:"weekly"})]),_:1},8,["modelValue","onChange"])]),_:1}),e.form.schedule_type!=="manual"?(A(),O(b,{key:0,label:"调度值",prop:"schedule_value"},{default:i(()=>[e.form.schedule_type==="once"?(A(),P(Q,{key:0},[t(c,{modelValue:e.onceDateTime,"onUpdate:modelValue":n[3]||(n[3]=u=>e.onceDateTime=u),type:"datetime",placeholder:"选择日期和时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",clearable:!1,editable:!1,style:{width:"100%"},onChange:e.updateScheduleValue},null,8,["modelValue","onChange"]),Ae,e.selectedTasks.length>3?(A(),O(w,{key:0,type:"warning",closable:!1,"show-icon":"",title:"注意：多个任务设置为同一时间执行可能会导致系统负载过高",description:"任务将被添加到队列中并按照并发限制执行，但可能会导致部分任务延迟执行",style:{"margin-top":"10px"}})):Y("",!0)],64)):e.form.schedule_type==="daily"?(A(),P(Q,{key:1},[t(S,{modelValue:e.dailyTime,"onUpdate:modelValue":n[4]||(n[4]=u=>e.dailyTime=u),placeholder:"选择时间",format:"HH:mm:ss","value-format":"HH:mm:ss",clearable:!1,editable:!1,style:{width:"100%"},onChange:e.updateScheduleValue},null,8,["modelValue","onChange"]),xe],64)):e.form.schedule_type==="weekly"?(A(),P(Q,{key:2},[C("div",Ie,[t(D,{modelValue:e.weeklyDay,"onUpdate:modelValue":n[5]||(n[5]=u=>e.weeklyDay=u),placeholder:"选择星期",style:{width:"120px"},onChange:e.updateScheduleValue,clearable:!1},{default:i(()=>[t(g,{label:"周日",value:"0"}),t(g,{label:"周一",value:"1"}),t(g,{label:"周二",value:"2"}),t(g,{label:"周三",value:"3"}),t(g,{label:"周四",value:"4"}),t(g,{label:"周五",value:"5"}),t(g,{label:"周六",value:"6"})]),_:1},8,["modelValue","onChange"]),t(S,{modelValue:e.weeklyTime,"onUpdate:modelValue":n[6]||(n[6]=u=>e.weeklyTime=u),placeholder:"选择时间",format:"HH:mm:ss","value-format":"HH:mm:ss",clearable:!1,editable:!1,style:{width:"calc(100% - 130px)"},onChange:e.updateScheduleValue},null,8,["modelValue","onChange"])]),Ue],64)):Y("",!0)]),_:1})):Y("",!0),t(b,{label:"状态",prop:"status"},{default:i(()=>[t(D,{modelValue:e.form.status,"onUpdate:modelValue":n[7]||(n[7]=u=>e.form.status=u),placeholder:"请选择状态",style:{width:"100%"}},{default:i(()=>[t(g,{label:"激活",value:"active"}),t(g,{label:"停用",value:"inactive"}),t(g,{label:"暂停",value:"paused"})]),_:1},8,["modelValue"])]),_:1}),t(b,{label:"关联发现任务"},{default:i(()=>{var u,j;return[C("div",Re,[C("div",ze,[C("div",Ee,[t(I,{modelValue:e.searchKeyword,"onUpdate:modelValue":n[8]||(n[8]=h=>e.searchKeyword=h),placeholder:"搜索发现任务ID",clearable:"",onInput:e.searchDiscoveryTasks,style:{width:"250px"}},{append:i(()=>[t(T,{onClick:e.searchDiscoveryTasks},{default:i(()=>[t(E,null,{default:i(()=>[t(U)]),_:1})]),_:1},8,["onClick"])]),_:1},8,["modelValue","onInput"]),C("div",He,[t(T,{size:"small",type:"primary",onClick:e.selectAllAvailableTasks},{default:i(()=>[L("全选可用任务")]),_:1},8,["onClick"]),t(T,{size:"small",onClick:e.clearAllSelection},{default:i(()=>[L("清空选择")]),_:1},8,["onClick"])])]),C("div",Le,[t(R,{type:"info"},{default:i(()=>[L("已选择 "+Z(e.form.discovery_task_ids.length)+" 个任务",1)]),_:1}),t(B,{content:"每个发现任务只能被一个调度任务关联",placement:"top"},{default:i(()=>[t(E,{class:"info-icon"},{default:i(()=>[t(q)]),_:1})]),_:1}),((u=r.$props)==null?void 0:u.mode)==="edit"?(A(),O(B,{key:0,content:"已关联的任务会自动显示为已选中状态",placement:"top"},{default:i(()=>[t(R,{type:"success"},{default:i(()=>[L("已关联任务自动选中")]),_:1})]),_:1})):Y("",!0)])]),((j=r.$props)==null?void 0:j.mode)==="edit"?(A(),O(w,{key:0,type:"info",closable:!1,"show-icon":"",title:"已关联任务处理说明",style:{"margin-bottom":"15px"}},{default:i(()=>[Me]),_:1})):Y("",!0),ke((A(),O(K,{ref:"taskTableRef",data:e.filteredTasks||[],style:{width:"100%"},border:"",height:"350px",onSelectionChange:e.handleSelectionChange,"row-key":h=>h.id,"default-sort":{prop:"id",order:"ascending"}},{default:i(()=>[t(z,{type:"selection",width:"55",align:"center",selectable:e.isTaskSelectable,"reserve-selection":""},null,8,["selectable"]),t(z,{prop:"id",label:"任务ID",width:"80",align:"center",sortable:""}),t(z,{prop:"task_name",label:"任务名称","min-width":"180","show-overflow-tooltip":"",sortable:""}),t(z,{prop:"task_type",label:"任务类型",width:"120",align:"center",sortable:""}),t(z,{prop:"status",label:"状态",width:"120",align:"center",sortable:""},{default:i(h=>[t(R,{type:h.row.schedule_task_id&&h.row.schedule_task_id!==e.form.id?"danger":"success",size:"small"},{default:i(()=>[L(Z(h.row.schedule_task_id&&h.row.schedule_task_id!==e.form.id?"已关联":"可选择"),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data","onSelectionChange","row-key"])),[[G,e.tasksLoading]]),C("div",Fe,[t(J,{"current-page":e.currentPage,"onUpdate:currentPage":n[9]||(n[9]=h=>e.currentPage=h),"page-size":e.pageSize,"onUpdate:pageSize":n[10]||(n[10]=h=>e.pageSize=h),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:e.totalTasks,onSizeChange:e.handleSizeChange,onCurrentChange:e.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])])]}),_:1}),e.form.schedule_type!=="manual"?(A(),O(b,{key:1},{default:i(()=>[t(w,{type:"info",closable:!1,"show-icon":"",title:"并发控制",description:"系统会自动控制并发执行的任务数量，以保证系统性能。当多个任务同时触发时，将会按照队列顺序执行。"})]),_:1})):Y("",!0),t(b,null,{default:i(()=>[t(T,{type:"primary",onClick:e.submitForm},{default:i(()=>[L("提交")]),_:1},8,["onClick"]),t(T,{onClick:e.cancel},{default:i(()=>[L("取消")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model","rules"])])}const Ke=ue(Ce,[["render",Oe],["__scopeId","data-v-3b42c9a4"]]);export{Ke as S,Be as a,Pe as d,je as g,qe as r,Ne as u};
