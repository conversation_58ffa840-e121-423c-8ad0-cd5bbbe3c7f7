## [2.2.0.6] - 2025年5月26日

### 更新
- 在变更管理附件管理中新增【补充资料】上传功能，支持与其他附件相同的文件格式
- 添加补充资料文件存储路径配置，需要在.env中配置FILE_UPLOAD_SUPPLEMENTARY_PATH
- 新增数据库字段supplementary_material用于存储补充资料文件路径
- 在变更详情页面的附件管理上方新增【变更模板下载】功能，支持选择模板名称后直接下载
- 新增完整的API文档，涵盖所有模块的接口说明、参数格式、响应示例和错误码说明

### 优化
- 优化变更管理页面数据查询，改为使用v_ops_change_management视图获取数据，提高查询效率和数据一致性
- 优化变更管理页面默认显示条件，默认显示OA流程和签字存档均为"未上传"的记录，按变更编号从大到小排序
- 优化变更详情页面布局，提高空间利用率和用户体验
- 将变更编号改为单独显示，不占用表单框位置，节省空间
- 将变更名称调整为表单第一位，符合用户操作习惯
- 将所有变更字段整合到一个统一的"变更信息"卡片中，提高页面一致性
- 优化表单布局为三行结构：变更名称(全宽) + 基本信息(3列) + 系统人员(2列)
- 移除冗余的详细展示区域和分隔线设计，简化页面结构
- 统一使用Element Plus表单组件，保持一致的交互体验
- 使用统一的视觉风格和Element Plus设计规范
- 添加响应式设计，确保在移动端的良好显示效果

### 修复
- 修复补充资料上传时提示"无效的类型"的问题，完善后端API对supplementary_material文件类型的支持
- 修复前端无法显示已上传的补充资料文件的问题，在后端API查询中添加supplementary_material字段
- 修复nginx配置缺少/files/路径转发导致文件下载失败的问题，添加文件下载代理配置

## [2.2.0.5] - 2025年5月23日

### 更新
- 在运维管理栏目下新增变更模板管理页面，提供模板上传、预览和下载功能
- 支持Word和Excel格式的变更模板文件管理
- 实现模板文件在线预览功能，无需下载即可查看文件内容
- 添加模板文件上传功能，支持文件类型和大小验证
- 为变更模板管理页面的删除按钮添加用户级别的权限控制，只有拥有删除权限的用户才能看到删除按钮

### 优化
- 优化变更模板管理页面的用户界面，使用卡片样式展示文件信息
- 移除文件预览功能，直接提供下载选项，解决内网环境无法预览的问题
- 优化文件上传组件，提供文件类型和大小限制提示，增强文件后缀检查
- 优化模板列表展示，移除ID列，增加更新时间和更新人字段展示
- 优化文件上传路径配置，添加专用的变更模板文件存储路径
- 优化变更模板管理页面，移除文件类型列，简化界面展示
- 优化变更模板管理功能，添加覆盖上传按钮，支持更新现有模板文件
- 优化中文文件名处理机制，使用iconv-lite库实现更准确的编码检测和转换
- 优化变更模板管理页面的权限控制，使用role_code机制控制删除按钮的显示
- 优化文件上传错误处理，提供友好的错误提示，避免显示技术错误信息

### 修复
- 修复变更模板管理表中file_type字段长度不足导致的上传失败问题
- 修复中文文件名上传时出现乱码的问题，优化文件名编码处理机制
- 修复变更模板覆盖上传功能无法正常工作的问题，添加文件上传中间件
- 修复文件上传后原始文件名显示乱码的问题，从前端直接传递原始文件名
- 修复文件上传中断问题，简化文件上传处理流程，增强错误处理
- 修复覆盖上传功能中sanitizeParam未定义的问题，统一参数处理函数
- 修复文件上传验证问题，确保只允许Word和Excel格式的文件上传
- 修复覆盖上传功能中文件类型验证问题，确保只允许.doc、.docx、.xls、.xlsx格式的文件
- 修复变更模板上传路径配置问题，确保文件正确存储在指定目录
- 修复上传文件后显示文件名乱码的问题，优化文件名编码处理逻辑
- 修复变更模板文件上传到错误目录的问题，确保文件存储在change_templates目录
- 修复数据库中文件名中文字符保存不正确的问题，优化数据库编码设置
- 修复上传中文文件名的文件后数据库中显示乱码的问题，完善文件名编码处理机制
- 修复变更模板管理中created_by和updated_by字段保存为system的问题，改为保存当前登录用户的username
- 修复中文文件名在数据库中保存为乱码的问题，增强文件名编码处理算法
- 修复特定中文文件名编码问题，添加专门的中文文件名处理工具
- 修复下载文件时无法使用原始文件名的问题，实现直接下载功能
- 修复当前登录用户获取为system的问题，改为使用auth.js中的getUsername函数获取，默认用户名设置为admin
- 统一解决所有文件格式的中文文件名乱码问题，使用硬编码方式处理特定文件类型的乱码，支持Word、Excel、PDF等多种文件格式
- 修复模板预览功能，添加加载状态和错误处理，优化用户体验

## [2.2.0.4] - 2025年5月22日

### 更新
- 变更管理页面变更系统支持多选，提高灵活性
- 变更管理页面搜索栏增加变更系统搜索条件，方便按系统筛选变更记录
- 变更详情页面增加多选选项的详细展示区域，直观展示变更涉及的系统和实施人
- 系统管理责任公司页面增加必填字段验证，确保关键信息完整性
- 系统管理责任公司页面主岗和备岗字段改为从用户表中选择，提高数据准确性
- 系统管理责任公司页面JR/T 0059-2010备份能力标准字段使用数据字典Q类型，规范数据存储
- 系统管理责任公司页面业务线条字段使用数据字典R类型，规范数据存储
- 系统管理责任公司页面业务系统大类字段使用数据字典S类型，规范数据存储

### 优化
- 变更管理页面取消变更描述字段的填入和展示，简化变更管理流程
- 优化变更详情页面的用户体验，使用卡片和标签样式展示多选字段，提高可读性
- 变更详情页面下拉选项框支持预输入快速搜索，提高用户选择效率
- 系统管理责任公司页面必填字段添加红色星号标注，提高用户体验
- 系统管理责任公司页面表格中显示用户真实姓名而非用户名，提高可读性
- 系统管理责任公司页面JR/T 0059-2010备份能力标准字段前端展示dict_name，后端存储dict_code，提高数据一致性
- 系统管理责任公司页面搜索栏中的主岗字段改为下拉选择框，支持快速筛选
- 系统管理责任公司页面业务线条和业务系统大类字段前端展示dict_name，后端存储dict_code，提高数据一致性
- 系统管理责任公司页面搜索栏增加业务线条和业务系统大类搜索条件，方便按业务类型筛选系统

### 修复
- 系统管理责任公司页面修复业务线条和业务系统大类搜索条件无效的问题，修改前后端代码确保搜索条件正确传递和处理

## [2.2.0.3] - 2025年5月21日

### 更新
- 实现事件管理页面，使用MVC架构组织代码，参考ITIL最佳实践
- 添加共享文件存储配置，支持两个节点服务共同访问同一个附件存储路径

### 优化
- 优化Nginx配置，添加共享文件服务配置，提高系统可靠性
- 提供详细的共享存储部署指南，包括NFS和SMB两种方案

### 修复
-

## [2.2.0.2] - 2025年5月21日

### 更新
- 变更详情页面附件管理增加手动刷新按钮，方便用户主动刷新附件状态

### 优化
- 统一变更管理页面中变更级别的字段名，前后端统一使用change_level，表格显示使用change_level_name_display
- 优化变更管理页面数据查询，直接从数据库获取数据而不依赖视图，提高查询效率
- 优化变更详情页面附件上传功能，上传或删除附件后智能多次刷新确保显示最新状态，提升用户体验
- 优化附件状态刷新机制，增加强制更新模式，解决缓存问题
- 优化变更详情页面数据刷新机制，实现父子组件协同刷新，确保数据一致性
- 优化变更详情页面附件管理刷新按钮位置，放置在右侧，提升界面布局美观度
- 优化变更管理页面操作按钮，将"查看"按钮改为"详情"按钮，提高用户体验
- 优化全局搜索功能，支持变更管理搜索结果，点击结果自动填入关键词并搜索，提升用户体验
- 优化页面权限管理，修复运维管理页面（变更管理和事件管理）在权限树中不可见的问题

### 修复
- 修复变更管理页面中变更实施人字段无法显示数据的问题
- 修复变更管理页面中变更级别字段无法显示数据的问题
- 修复变更管理页面搜索栏查询失败的问题
- 修复变更管理页面新增变更时created_by和updated_by字段未正确设置为当前用户的问题
- 修复变更管理页面搜索栏中使用变更级别无法搜索到结果的问题
- 修复变更管理页面表格中变更级别显示错误的问题
- 修复添加变更管理记录时参数数量不匹配的问题
- 修复变更管理和变更详情页面中变更级别选项显示问题，确保只显示数据字典P类型的选项
- 修复变更管理页面数据表中变更级别未显示的问题
- 修复变更详情页面附件上传功能中文件名乱码问题，优化文件上传处理逻辑
- 修复变更详情页面附件上传后状态刷新不及时的问题，实现智能多次刷新机制
- 修复变更详情页面附件状态刷新不生效的问题，解决数据缓存和组件更新问题
- 修复变更详情页面附件状态刷新需要刷新整个页面的问题，实现组件级别的精确刷新
- 修复变更管理页面新增变更时created_by和updated_by字段默认为admin或system的问题，确保使用当前登录用户
- 修复变更管理页面点击详情按钮时始终打开第一条记录的问题，确保正确打开对应记录

## [2.2.0.1] - 2025年5月20日

### 更新
- 优化变更管理页面界面，参考最新设计规范
- 在数据字典页面添加关键词搜索功能，支持同时搜索字典类型名称和字典名称
- 全栈实现变更管理功能，包括数据库表设计、后端API和前端界面
- 更新后端架构文档，添加新模块MVC架构实施指南

### 优化
- 优化侧边栏菜单布局，移除占位元素，改用CSS解决版本号遮挡问题

### 修复
- 修复数据字典新增时可能出现的主键冲突问题
- 修复数据字典表序列值不同步导致的插入错误
- 修复数据字典关键词搜索时的变量未定义错误
- 修复前端依赖问题，使用原生JavaScript实现日期格式化功能

## [*******] - 2025年5月19日

### 更新
- 更新前端和后端架构文档，添加详细的命名规范
- 完善模块划分规范，明确不同模块的命名前缀
- 添加新增模块开发规范，规范化开发流程
- 为运维管理模块添加页面权限配置，包括变更管理和事件管理页面
- 新增"运维管理"主菜单，位于自动发现菜单下方
- 在运维管理菜单下添加"变更管理"和"事件管理"两个子菜单
- 实现变更管理和事件管理的前端页面，包括搜索、列表展示等功能
- 使用"ops_"前缀命名运维管理相关页面，区别于CMDB模块
- 在设备管理页面添加序列号搜索功能
  - 前端：在搜索栏添加序列号输入框
  - 后端：在 get_cmdb_device_management 接口添加序列号搜索支持
- 优化变更管理页面界面，参考最新设计规范
  - 添加顶部导航标签，提高页面层次感
  - 重新设计搜索区域，使其更加紧凑直观
  - 新增OA流程和签字存档字段，完善变更管理流程
  - 优化表格结构，增加所属系统和变更级别列
  - 改进操作按钮样式，使用文本按钮节省空间

### 优化
- 统一前后端命名规则，提高代码可维护性
- 完善文档结构，便于新开发人员快速理解项目架构
- 规范化API接口和数据库表命名，保持一致性
- 优化页面权限管理，确保新增页面的权限控制与现有系统一致
- 使用Element Plus的图标组件，保持界面风格一致性
- 优化表格布局，固定操作列，提高用户体验
- 添加状态和优先级的颜色标识，增强视觉效果
- 统一命名规范，使模块划分更加清晰
- 优化设备管理页面的搜索功能，支持按序列号进行精确查询
- 保持与其他搜索条件一致的模糊匹配方式，提高用户体验
- 改进变更管理页面的搜索功能，支持按关键词进行全文搜索
- 优化变更管理表格的视觉效果，使用圆角标签显示状态信息
- 改进变更管理页面的响应式布局，适配不同屏幕尺寸


### 修复
-

## [2.1.0.1] - 2025年5月14日

### 更新
- 更新前端知识库组件文件名，从knowledge_base.vue改为ai_knowledge_base.vue
- 更新前端文档智能修订组件文件名，从document_editor.vue改为ai_document_editor.vue

### 优化
- 文件命名更加规范，与AI平台下的其他功能组件保持一致

### 修复
-


## [2.1.0.0] - 2025年5月12日

### 更新
- 新增"AI平台"作为独立的主菜单，位于报表中心菜单下方
- 添加"文档智能修订"功能，通过嵌入式iframe集成外部AI服务
- 预留"智能数据分析"功能卡片，为后续扩展做准备
- 添加AI平台相关的数据库表结构和页面权限配置

### 优化
- 优化AI平台页面的响应式布局，适配不同屏幕尺寸
- 使用卡片悬浮效果提升用户交互体验
- 优化iframe加载方式，提高外部服务的集成效率
- 完善AI平台的权限控制机制，确保安全访问
- 优化系统管理员责任表（公司）页面中"通用功能域"和"经营机构功能域"的展示方式

### 修复
- 修复菜单权限检查逻辑，确保新增页面的权限控制正常工作

## [*******6] - 2025年5月9日

### 更新
- 修改系统管理员责任表（公司）页面中"通用功能域"和"经营机构功能域"的展示方式
- 功能域数据现在以逗号分隔并且不换行显示，提高表格的紧凑性

### 优化
- 优化表格显示效果，使功能域数据更加清晰易读
- 保持核心功能域的红色标注，确保重要信息突出显示

## [*******5] - 2025年5月9日

### 更新
- 移除系统管理员责任表（公司）页面中"通用功能域"和"经营机构功能域"的查询条件
- 保留表格中的功能域显示和表单中的功能域选择功能

### 优化
- 简化搜索界面，减少不必要的筛选条件，提高用户体验
- 优化页面布局，使搜索区域更加紧凑

## [*******4] - 2025年5月9日

### 更新
- 在系统管理员责任表（公司）页面新增"通用功能域"和"经营机构功能域"字段，均为多选
- 创建功能域类型表（cmdb_function_domain_types），用于存储功能域数据
- 在表格中添加"通用功能域"和"经营机构功能域"列，并使用红色标注核心功能域
- 添加核心功能域标识，使用红色文字和"(核心)"标签突出显示

### 优化
- 在添加和编辑表单中添加"通用功能域"和"经营机构功能域"字段，使用多选下拉框实现
- 使用collapse-tags属性优化多选显示效果，提高用户体验
- 优化表单布局，保持各字段样式一致，提高用户体验

## [*******3] - 2025年5月9日

### 更新
- 在系统管理员责任表（公司）页面新增"等保等级"字段，包含"一级"、"二级"、"三级"三个选项
- 在表格中添加"等保等级"列，方便用户查看系统等保等级

### 优化
- 在搜索条件中添加"等保等级"筛选项，方便用户按等保等级筛选数据
- 在添加和编辑表单中添加"等保等级"字段，使用下拉选择框实现，提高用户体验
- 优化表单布局，保持各字段样式一致，提高用户体验

## [*******2] - 2025年5月9日

### 更新
- 在系统管理员责任表（公司）页面新增"信创状态"字段，包含"未信创"、"完成开发或测试"、"非全栈双轨"、"非全栈单轨"、"全栈双轨"、"全栈单轨"六个选项
- 在表格中添加"信创状态"列，方便用户查看系统信创进度

### 优化
- 在搜索条件中添加"信创状态"筛选项，方便用户按信创状态筛选数据
- 在添加和编辑表单中添加"信创状态"字段，使用下拉选择框实现，提高用户体验
- 优化表单布局，保持各字段样式一致，提高用户体验

## [*******1] - 2025年5月9日

### 更新
- 在系统管理员责任表（公司）页面新增"运行状态"字段，包含"建设中"、"运行中"、"已下线"三个选项
- 在表格中添加"运行状态"列，方便用户查看系统当前状态

### 优化
- 在搜索条件中添加"运行状态"筛选项，方便用户按系统状态筛选数据
- 在添加和编辑表单中添加"运行状态"字段，使用下拉选择框实现，提高用户体验
- 优化表单布局，保持各字段样式一致，提高用户体验

## [*******0] - 2025年5月9日

### 更新
- 在应用系统信息页面增加"功能用途"字段，位于主机名后面，用于描述应用系统的主要功能
- 在IP资产管理页面搜索栏增加"是否在线"搜索条件，可选"在线"和"离线"两个选项

### 优化
- 应用系统信息页面的"是否在线"字段优化了取值逻辑，从自动发现结果中进行判断
- IP资产管理页面的"虚拟机状态"字段优化了取值逻辑，从自动发现结果中进行判断，提高了状态判断的准确性
- 优化IP资产管理页面的水平滚动条显示位置，固定在当前屏幕底部，方便用户查看靠上的数据时进行水平滚动
- 改进所有页面的水平滚动条，使其始终可见，不需要鼠标悬停才显示，提高用户体验
- 调整IP资产管理页面搜索卡片高度，确保所有搜索项和按钮正常显示

### 修复
- 修复调度任务页面偶发的DOM操作错误，增强组件稳定性


## [*******] - 2025年5月8日

### 更新
- 在系统管理员责任表（公司）页面增加"技术路线"字段，位于建设方式字段后面，使用数据字典类型O
- 优化系统管理员责任表（公司）页面中"系统属性"字段，使用数据字典类型C，参考信创小类的实现方式

### 优化
- 优化系统管理员责任表（公司）页面中的数据字典字段实现，统一使用字典名称作为显示和搜索值
- 改进系统管理员责任表（公司）页面的搜索功能，修复系统属性字段搜索不到数据的问题
- 优化表单布局，保持各字段样式一致，提高用户体验

### 修复
- 修复系统管理员责任表（公司）页面中系统属性字段搜索不到数据的问题

## [*******] - 2025年5月7日

### 更新
- 在需求与问题收集页面增加"计划开发时间"和"开发周期(人天)"两个字段，位于方案类型字段后面
- 在表格中添加"计划开发时间"和"开发周期(人天)"列，方便用户查看和管理开发计划
- 在详情对话框中显示"计划开发时间"和"开发周期(人天)"信息，提供完整的问题详情

### 优化
- 优化需求与问题收集页面的表单布局，使新增字段与现有字段保持一致的样式
- 使用日期选择器组件实现计划开发时间字段，提高用户体验
- 使用数字输入框组件实现开发周期字段，限制只能输入整数，避免输入错误

### 修复
- 修复日期字段为空时导致的"invalid input syntax for type date"错误，优化日期字段处理逻辑
- 将"问题描述"、"提出时间"和"上报人"设置为必填字段，确保数据完整性
- 增加表单验证，防止用户提交不完整的问题记录

## [*******] - 2025年5月7日

### 更新
- 在应用系统信息页面增加"关联主从机IP"字段，位于主从角色字段后面
- 当主从角色不为单机时，关联主从机IP字段为必填项
- 支持在关联主从机IP字段中输入多个IP地址，使用英文逗号分隔
- 添加IP地址格式验证，确保输入的IP地址格式正确
- 将应用系统信息表单中的"归属业务系统名称"字段改为下拉选择框，选项来自系统管理员责任表（公司）
- 将"归属业务系统名称"字段设置为必填项，确保每个应用系统都有明确的归属
- 将搜索栏中的"归属业务系统名称"字段也改为下拉选择框，保持与表单一致的交互体验
- 将"部署应用"和"生产属性"字段设置为必填项，确保应用系统信息的完整性
- 将"备注"字段移动到表单的末尾位置，优化表单布局和用户体验

### 优化
- 优化问题收集表中问题描述字段的显示宽度，增加预览内容，提高用户体验
- 优化问题收集表页面，添加问题描述搜索功能，方便用户快速查找问题
- 优化问题收集表页面的操作列，固定在右侧并设置固定宽度，提高用户体验
- 优化主从角色判断逻辑，支持同时识别字典代码和字典名称，提高表单验证的准确性
- 优化版本更新脚本，在创建新版本时自动添加"更新"、"优化"和"修复"三个分类标题

### 修复
- 修复编辑表单打开时，主从角色为单机但关联主从IP字段仍显示为必填的问题
- 修复问题收集表页面导出数据时，文件名错误的问题

## [*******] - 2025年4月30日

### 更新
- 在报表中心的网络设备年限情况统计和实体服务器年限情况统计页面中增加"是否信创"筛选条件
- 优化筛选条件收藏功能，支持保存和应用"是否信创"筛选条件

## [*******] - 2025年4月30日

### 更新
- 为网络设备和实体服务器添加"是否信创"字段，实现与网络设备相同的验证逻辑
- 修复部分bug
- 优化问题收集表前端样式

## [*******] - 2025年4月29日

### 更新
- 优化网络设备信息表单，当"是否单点"选择"否"时，"互备主机IP"字段设为必填
- 优化网络设备信息表单，移除"架构模式"字段，保留数据库字段作为后期扩展
- 为实体服务器管理添加"是否单点"和"互备主机IP"字段，实现与网络设备相同的验证逻辑
- 优化虚拟机信息表单，将"是否存在弱密码"字段的值从"true/false"改为"是/否"，提高用户体验
- 优化全局搜索功能，点击搜索结果时自动将IP带入目标页面的搜索条件中，提升用户体验
- 增强全局搜索功能，保留搜索结果状态，搜索结果会保存在浏览器的localStorage中，用户离开页面后返回时无需重新搜索
- 优化报表中心的网络设备年限统计和服务器年限统计页面，保留查询结果状态，提升用户体验

### 修复
- 修复监控IP列表插入时可能出现的主键冲突问题
- 修复全局搜索中应用系统信息的路由路径错误问题

## [*******] - 2025年4月28日

### 更新
- 优化调度任务选择关联任务的逻辑，已关联的任务会自动显示为已选中状态
- 修复调度任务表单中状态排序功能，支持按状态正确排序
- 优化Nginx配置，提升前端性能和安全性
- 修复发现任务管理页面按调度任务ID查询时的SQL错误
- 优化队列状态对话框，添加滑动条，提高用户体验
- 修复主备切换时登录卡住的问题，增强系统可靠性
- 优化数据字典缓存机制，提高系统性能和数据一致性

### 修复
- 修复用户首次登录卡住的的问题

### 优化
- 调度任务表单中添加"已关联任务自动选中"提示，提高用户体验
- 添加"已关联任务处理说明"提示框，说明如何取消关联任务
- 修复表单中的错误处理，提高代码健壮性
- 网络设备信息表单优化：
  - 当"是否单点"选择"否"时，"互备主机IP"字段设为必填
  - 支持在"互备主机IP"字段中输入多个IP地址，使用英文逗号分隔
  - 添加IP地址格式验证，确保输入的IP地址格式正确
- 实体服务器管理表单优化：
  - 添加"是否单点"和"互备主机IP"字段，与网络设备保持一致
  - 当"是否单点"选择"否"时，"互备主机IP"字段设为必填
  - 支持在"互备主机IP"字段中输入多个IP地址，使用英文逗号分隔
  - 添加IP地址格式验证，确保输入的IP地址格式正确
- 全面优化Nginx配置，包括：
  - 启用Gzip压缩，减少传输数据大小
  - 配置浏览器缓存策略，提高页面加载速度
  - 优化静态资源处理，减少服务器负载
  - 增强安全相关HTTP头，提高系统安全性
  - 配置服务器端缓存，提高响应速度
  - 优化负载均衡设置，提高系统可靠性
  - 添加健康检查机制，提高系统稳定性
  - 增强主备切换机制，解决登录卡住问题
  - 优化API请求处理，增加超时设置和缓冲区大小
  - 添加服务器标识，便于问题排查
- 队列状态对话框优化：
  - 添加滑动条，限制对话框高度，使其更加紧凑
  - 显示排队中任务的数量，方便用户了解队列状态
  - 自定义滚动条样式，提高用户体验
  - 优化表格高度，避免对话框过长
- 数据字典缓存优化：
  - 将缓存过期时间从5分钟延长至30分钟，减少数据库查询次数
  - 实现数据字典变更时自动清除缓存，确保数据一致性
  - 优化缓存更新机制，在添加、更新、删除操作后立即刷新缓存
  - 提高系统响应速度，特别是对频繁访问的数据字典查询

## [*******] - 2025年4月24日

### 更新
- 优化IP资产管理页面搜索框样式，与网络设备页面保持一致
- 修改IP资产管理页面，移除CMDB登记状态的搜索条件和数据列显示
- 在IP资产管理页面中，为管理状态和是否虚拟机字段提供下拉菜单选择
- 优化发现结果查看页面的批量登记功能，支持直接登记到IP资产管理
- 修复用户日志记录时的唯一约束冲突问题，提高系统稳定性

## [*******] - 2025年4月24日

### 更新
- 用户管理权限控制优化：除了 admin 用户外，其他用户只能查看和编辑自己的信息
- 修复普通用户无法编辑自己信息的问题
- 优化用户权限管理逻辑，确保普通用户不能修改角色权限
- 将项目中的 bcrypt 替换为 bcryptjs，解决内网环境部署时的兼容性问题

## [*******] - 2025年4月24日

### 更新
- 扩充用户管理，增加用户姓名、联系电话、邮箱、企业微信ID字段
- 增加用户访问页面的权限设置，当用户没有该页面的权限时，则无法看见该页面
- 新增页面权限管理功能，支持细粒度的页面权限控制
- 新增页面权限相关的数据库表结构
- 重构路由守卫，增加页面权限验证机制
- 将用户管理相关API独立成模块

### 优化
- 改进用户管理界面，增加更多用户信息字段
- 优化菜单显示逻辑，根据用户权限动态显示菜单项
- 完善用户登录逻辑，增加页面权限验证


## [2.0.2.1] - 2025年4月23日

### 更新
- 新增调度任务管理功能，支持将多个发现任务组织到一个调度任务中统一管理
- 支持手动、一次性、每日和每周四种调度类型，并提供日历和时间选择器
- 添加调度任务执行历史记录功能，跟踪每次执行的结果和状态
- 实现任务队列管理机制，支持最多100个任务的批量调度
- 重构发现任务控制器，将任务队列管理功能独立出来
- 新增调度任务相关的数据库表结构
- 实现调度任务与发现任务的关联机制

### 优化
- 使用日历和时间选择器替代文本输入，提高调度值设置的用户体验
- 为不同的调度类型提供清晰的格式说明和示例
- 改进任务队列管理机制，自动控制并发执行的任务数量
- 优化调度任务详情页面，提供全面的任务信息和执行历史

## [2.0.2.0] - 2025年4月22日

### 更新
- 新增自动发现功能
- 在CMDB发现任务管理页面添加任务ID列，便于任务追踪和管理
- 在CMDB发现任务中添加任务运行时间记录和显示功能

### 优化
- 改进任务队列状态对话框，使其只能通过关闭按钮关闭，避免意外关闭
- 添加查看队列状态按钮，允许用户随时查看批量任务执行进度
- 统一导出数据按钮的图标样式，保持界面一致性
- 修复发现结果查看页面的重置功能，使其正确重置任务ID字段
- 优化自动发现相关页面的表格布局，包括固定操作列和调整列宽
- 美化滚动条样式，使其与资产管理页面保持一致
- 改进分页组件显示，确保正确显示当前设置的每页条数

### 说明
- 明确CMDB发现任务执行消耗的是后端服务器资源，而非用户本地资源

## [*******] - 2025年4月21日

### 更新
- 新增用户登录记录功能，自动记录用户登录时间、IP、状态等信息

### 优化
- 登录失败时记录失败原因，便于安全分析
- 记录用户登录的设备信息，提高系统安全性

## [*******] - 2025年4月21日

### 更新
- 新增报表中心
- 提供网络设备年限情况统计报表
- 提供实体服务器年限情况统计报表
- 提供查询条件的收藏功能

### 修复
- 修复了视图v_cmdb_server_management统计数据有重复的问题
- 修复了视图v_cmdb_device_management统计数据有重复的问题

## [*******] - 2025年4月18日

### 更新
- 新增左侧菜单栏"需求与问题"栏目，位置放在配置管理下方
- 将"问题收集表"从资产管理移动到新的"需求与问题"栏目中

### 优化
- 增加表单字段验证功能，添加必填字段的验证
- 修改表单页面的标题，与侧边栏标题保持一致

## [*******] - 2025年4月16日

### 更新
- 更新全局搜索样式
- 主机扫描结果增加查询条件 增加搜索字段：管理员1，管理员2，指定管理员，管理状态，是否虚拟机，CMDB登记状态（手动）

### 优化
- 优化数据库中cmdb_user_logs表存在明文记录密码的情况
- 优化前端暴露密码哈希值的问题

### 修复
- 修复菜单栏和首页/展示不一致问题


## [2.0.0.6] - 2025年4月10日

### 更新
- 在侧边栏底部添加版本信息显示功能
- 优化侧边栏滚动条样式，提升用户体验

### 优化
- 美化侧边栏滚动条，增加宽度并改进外观
- 优化版本信息对话框层级，确保始终显示在最前端
- 添加全局滚动条样式，提升整体界面一致性
- 使用环境变量配置后端服务地址，提高系统可维护性

### 修复
- 修复后端 CORS 配置问题，解决跨域请求错误
- 修复版本信息对话框与资产管理表格重叠问题

## [2.0.0.5] - 2025年4月9日

### 修复
- 解决后端jwt硬编码问题
- 解决前后端token过期时间不一致问题

## [2.0.0.1] - 2025年3月31日

### 创建
- 创建cmdb系统