/**
 * 版本配置文件
 * 在此处集中管理前端版本信息
 * 使用硬编码的版本号，在构建时通过脚本更新
 */

// 当前版本号
// 注意: 这个值应该与 package.json 中的版本号保持一致
// 在运行 npm run update-version 时会自动更新
// 请不要手动修改这个值
// VERSION_PLACEHOLDER
export const version = '2.2.0.7';

// 应用标题 - 从环境变量中获取
export const appTitle = import.meta.env.VITE_APP_TITLE || '长江期货 CMDB 系统';

// 版权信息 - 从环境变量中获取
export const copyright = import.meta.env.VITE_APP_COPYRIGHT || '© 2025 长江期货 CMDB 系统';
