import{_ as O,z as j,v as H,A as J,c as B,a as e,w as a,f as p,b as n,h as u,m as v,x,t as f,B as K,C as L,e as h}from"./index-Bo9zKuAo.js";import{u as P,w as Q,F as W}from"./FileSaver.min-BlIK9FxR.js";const X={components:{Plus:J,Search:H,Download:j},data(){return{userArr:[],loading:!1,hasDeletePermission:localStorage.getItem("role_code")?localStorage.getItem("role_code").includes("D"):!1,hasUpdatePermission:localStorage.getItem("role_code")?localStorage.getItem("role_code").includes("U"):!1,hasInsertPermission:localStorage.getItem("role_code")?localStorage.getItem("role_code").includes("I"):!1,dialogVisible:{add:!1,edit:!1,delete:!1,detail:!1},detailData:{},search:{description:null,raised_time:null,raised_by:null,is_solved:null,solution_type:null,total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,description:"",raised_time:"",raised_by:"",is_solved:"",solution_type:"",planned_dev_time:"",dev_cycle_days:null,solution:"",solved_by:"",solved_time:"",created_time:""},rules:{description:[{required:!0,message:"请输入需求问题描述",trigger:"blur"}],raised_time:[{required:!0,message:"请选择提出日期",trigger:"change"}],raised_by:[{required:!0,message:"请输入上报人",trigger:"blur"}]}}},mounted(){this.loadData(),this.checkPermissions()},methods:{checkPermissions(){const s=localStorage.getItem("role_code");console.log("当前用户角色权限:",s),this.hasDeletePermission=s?s.includes("D"):!1,this.hasUpdatePermission=s?s.includes("U"):!1,this.hasInsertPermission=s?s.includes("I"):!1,console.log("权限状态:",{删除权限:this.hasDeletePermission,更新权限:this.hasUpdatePermission,新增权限:this.hasInsertPermission})},handlePageChange(s){this.search.currentPage=s,this.loadData()},handlePageSizeChange(s){this.search.pageSize=parseInt(s),this.search.currentPage=1,this.loadData()},handleSortChange({prop:s,order:l}){this.search.sortProp=s,this.search.sortOrder=l==="ascending"?"asc":"desc",this.loadData()},async loadData(){try{this.loading=!0;const s=await this.$axios.post("/api/get_cmdb_issue_collection",this.search);this.userArr=s.data.msg,this.search.total=s.data.total}catch(s){console.error("数据加载失败:",s),this.$message.error("数据加载失败")}finally{this.loading=!1}},async submitAdd(){var s,l;try{await this.$refs.addForm.validate();const i={...this.formData};i.planned_dev_time===""&&(i.planned_dev_time=null),i.raised_time===""&&(i.raised_time=null),i.solved_time===""&&(i.solved_time=null),await this.$axios.post("/api/add_cmdb_issue_collection",i),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(i){if(i===!1){this.$message.warning("请填写必填字段");return}console.error("添加失败:",i),this.$message.error("添加失败: "+(((l=(s=i.response)==null?void 0:s.data)==null?void 0:l.msg)||i.message))}},async submitEdit(){var s,l;try{await this.$refs.editForm.validate();const i={...this.formData};i.planned_dev_time===""&&(i.planned_dev_time=null),i.raised_time===""&&(i.raised_time=null),i.solved_time===""&&(i.solved_time=null),await this.$axios.post("/api/update_cmdb_issue_collection",i),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(i){if(i===!1){this.$message.warning("请填写必填字段");return}console.error("更新失败:",i),this.$message.error("更新失败: "+(((l=(s=i.response)==null?void 0:s.data)==null?void 0:l.msg)||i.message))}},async submitDelete(){try{await this.$axios.post("/api/del_cmdb_issue_collection",this.formData),this.$message.success("删除成功"),this.loadData(),this.dialogVisible.delete=!1}catch(s){console.error("删除失败:",s),this.$message.error("删除失败")}},handleAdd(){this.dialogVisible.add=!this.dialogVisible.add,this.formData={},this.formData.is_solved="未解决"},handleEdit(s,l){this.dialogVisible.edit=!0,this.formData.id=l.id,this.formData.description=l.description,this.formData.raised_time=l.raised_time,this.formData.raised_by=l.raised_by,this.formData.is_solved=l.is_solved,this.formData.solution_type=l.solution_type||"",this.formData.planned_dev_time=l.planned_dev_time||"",this.formData.dev_cycle_days=l.dev_cycle_days||null,this.formData.solution=l.solution,this.formData.solved_by=l.solved_by,this.formData.solved_time=l.solved_time,this.formData.notification=l.notification},handleDelete(s,l){this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=l.id,this.formData.description=l.description},showDetail(s){this.detailData={...s},this.dialogVisible.detail=!0},resetSearch(){this.search={description:null,raised_time:null,raised_by:null,is_solved:null,solution_type:null,total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},exportData(){const l=this.$refs.table.columns,i=l.map(r=>r.label),S=this.userArr.map(r=>l.map(D=>r[D.property])),t=[i,...S],m=P.aoa_to_sheet(t),c=P.book_new();P.book_append_sheet(c,m,"Sheet1");const d=Q(c,{bookType:"xlsx",type:"array"}),y=new Blob([d],{type:"application/octet-stream"});W.saveAs(y,"需求与问题收集.xlsx")}}},Z={class:"user-manage"},$={class:"form-container"},ee={class:"dialog-footer"},le={class:"form-container"},te={class:"dialog-footer"},ae={class:"detail-container"},oe={class:"detail-text"},se={class:"detail-text"},ie={class:"dialog-footer"},de={class:"button-container"},re=["onClick"],ne={key:1},ue=["onClick"],me={style:{display:"flex","white-space":"nowrap","justify-content":"center",gap:"5px"}},_e={class:"pagination"};function pe(s,l,i,S,t,m){const c=n("el-input"),d=n("el-form-item"),y=n("el-date-picker"),r=n("el-option"),D=n("el-select"),M=n("el-input-number"),k=n("el-form"),b=n("el-button"),w=n("el-dialog"),A=n("el-alert"),g=n("el-descriptions-item"),Y=n("el-tag"),q=n("el-descriptions"),V=n("el-col"),C=n("el-row"),R=n("Search"),z=n("el-icon"),E=n("Plus"),F=n("Download"),I=n("el-card"),_=n("el-table-column"),G=n("el-table"),N=n("el-pagination"),T=L("loading");return h(),B("div",Z,[e(w,{modelValue:t.dialogVisible.add,"onUpdate:modelValue":l[11]||(l[11]=o=>t.dialogVisible.add=o),title:"添加需求问题记录",width:"850","align-center":""},{footer:a(()=>[p("div",ee,[e(b,{size:"large",onClick:l[10]||(l[10]=o=>t.dialogVisible.add=!1)},{default:a(()=>[u("返回")]),_:1}),e(b,{size:"large",type:"primary",onClick:m.submitAdd},{default:a(()=>[u("确定")]),_:1},8,["onClick"])])]),default:a(()=>[p("div",$,[e(k,{ref:"addForm",model:t.formData,rules:t.rules,"label-position":"right","label-width":"130px"},{default:a(()=>[e(d,{label:"需求问题描述:",prop:"description",rules:[{required:!0,message:"请输入需求问题描述",trigger:"blur"}]},{default:a(()=>[e(c,{modelValue:t.formData.description,"onUpdate:modelValue":l[0]||(l[0]=o=>t.formData.description=o),type:"textarea",autosize:{minRows:5,maxRows:12},style:{width:"600px","font-size":"14px"},placeholder:"请详细描述需求问题，支持多行文本",clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"提出日期:",prop:"raised_time",rules:[{required:!0,message:"请选择提出日期",trigger:"change"}]},{default:a(()=>[e(y,{modelValue:t.formData.raised_time,"onUpdate:modelValue":l[1]||(l[1]=o=>t.formData.raised_time=o),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"380px","font-size":"14px"},placeholder:"选择日期"},null,8,["modelValue"])]),_:1}),e(d,{label:"上报人:",prop:"raised_by",rules:[{required:!0,message:"请输入上报人",trigger:"blur"}]},{default:a(()=>[e(c,{modelValue:t.formData.raised_by,"onUpdate:modelValue":l[2]||(l[2]=o=>t.formData.raised_by=o),style:{width:"380px","font-size":"14px"},clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"是否解决:"},{default:a(()=>[e(D,{modelValue:t.formData.is_solved,"onUpdate:modelValue":l[3]||(l[3]=o=>t.formData.is_solved=o),style:{width:"380px","font-size":"14px"}},{default:a(()=>[e(r,{label:"已解决",value:"已解决"}),e(r,{label:"未解决",value:"未解决"})]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"方案类型:"},{default:a(()=>[e(D,{modelValue:t.formData.solution_type,"onUpdate:modelValue":l[4]||(l[4]=o=>t.formData.solution_type=o),style:{width:"380px","font-size":"14px"},placeholder:"请选择方案类型"},{default:a(()=>[e(r,{label:"新需求开发",value:"新需求开发"}),e(r,{label:"代码优化",value:"代码优化"}),e(r,{label:"BUG修复",value:"BUG修复"}),e(r,{label:"数据处理",value:"数据处理"}),e(r,{label:"不做任何处理",value:"不做任何处理"})]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"计划开发日期:"},{default:a(()=>[e(y,{modelValue:t.formData.planned_dev_time,"onUpdate:modelValue":l[5]||(l[5]=o=>t.formData.planned_dev_time=o),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"380px","font-size":"14px"},placeholder:"选择计划开发日期"},null,8,["modelValue"])]),_:1}),e(d,{label:"开发周期(人天):"},{default:a(()=>[e(M,{modelValue:t.formData.dev_cycle_days,"onUpdate:modelValue":l[6]||(l[6]=o=>t.formData.dev_cycle_days=o),min:0,precision:0,style:{width:"380px","font-size":"14px"},placeholder:"请输入开发周期"},null,8,["modelValue"])]),_:1}),e(d,{label:"解决方案:"},{default:a(()=>[e(c,{modelValue:t.formData.solution,"onUpdate:modelValue":l[7]||(l[7]=o=>t.formData.solution=o),type:"textarea",autosize:{minRows:4,maxRows:10},style:{width:"600px","font-size":"14px"},placeholder:"请详细描述解决方案，支持多行文本",clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"解决人:"},{default:a(()=>[e(c,{modelValue:t.formData.solved_by,"onUpdate:modelValue":l[8]||(l[8]=o=>t.formData.solved_by=o),style:{width:"380px","font-size":"14px"},clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"解决日期:"},{default:a(()=>[e(y,{modelValue:t.formData.solved_time,"onUpdate:modelValue":l[9]||(l[9]=o=>t.formData.solved_time=o),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"380px","font-size":"14px"},placeholder:"选择日期"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),e(w,{modelValue:t.dialogVisible.edit,"onUpdate:modelValue":l[23]||(l[23]=o=>t.dialogVisible.edit=o),title:"编辑需求问题记录",width:"850","align-center":""},{footer:a(()=>[p("div",te,[e(b,{size:"large",onClick:l[22]||(l[22]=o=>t.dialogVisible.edit=!1)},{default:a(()=>[u("取消")]),_:1}),e(b,{size:"large",type:"primary",onClick:m.submitEdit},{default:a(()=>[u("更新")]),_:1},8,["onClick"])])]),default:a(()=>[p("div",le,[e(k,{ref:"editForm",model:t.formData,rules:t.rules,"label-position":"right","label-width":"130px"},{default:a(()=>[e(d,{label:"需求问题描述:",prop:"description",rules:[{required:!0,message:"请输入需求问题描述",trigger:"blur"}]},{default:a(()=>[e(c,{modelValue:t.formData.description,"onUpdate:modelValue":l[12]||(l[12]=o=>t.formData.description=o),type:"textarea",autosize:{minRows:5,maxRows:12},style:{width:"600px","font-size":"14px"},placeholder:"请详细描述问题，支持多行文本",clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"提出日期:",prop:"raised_time",rules:[{required:!0,message:"请选择提出日期",trigger:"change"}]},{default:a(()=>[e(y,{modelValue:t.formData.raised_time,"onUpdate:modelValue":l[13]||(l[13]=o=>t.formData.raised_time=o),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"380px","font-size":"14px"},placeholder:"选择日期"},null,8,["modelValue"])]),_:1}),e(d,{label:"上报人:",prop:"raised_by",rules:[{required:!0,message:"请输入上报人",trigger:"blur"}]},{default:a(()=>[e(c,{modelValue:t.formData.raised_by,"onUpdate:modelValue":l[14]||(l[14]=o=>t.formData.raised_by=o),style:{width:"380px","font-size":"14px"},clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"是否解决:"},{default:a(()=>[e(D,{modelValue:t.formData.is_solved,"onUpdate:modelValue":l[15]||(l[15]=o=>t.formData.is_solved=o),style:{width:"380px","font-size":"14px"}},{default:a(()=>[e(r,{label:"已解决",value:"已解决"}),e(r,{label:"未解决",value:"未解决"})]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"方案类型:"},{default:a(()=>[e(D,{modelValue:t.formData.solution_type,"onUpdate:modelValue":l[16]||(l[16]=o=>t.formData.solution_type=o),style:{width:"380px","font-size":"14px"},placeholder:"请选择方案类型"},{default:a(()=>[e(r,{label:"新需求开发",value:"新需求开发"}),e(r,{label:"代码优化",value:"代码优化"}),e(r,{label:"BUG修复",value:"BUG修复"}),e(r,{label:"数据处理",value:"数据处理"}),e(r,{label:"不做任何处理",value:"不做任何处理"})]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"计划开发日期:"},{default:a(()=>[e(y,{modelValue:t.formData.planned_dev_time,"onUpdate:modelValue":l[17]||(l[17]=o=>t.formData.planned_dev_time=o),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"380px","font-size":"14px"},placeholder:"选择计划开发日期"},null,8,["modelValue"])]),_:1}),e(d,{label:"开发周期(人天):"},{default:a(()=>[e(M,{modelValue:t.formData.dev_cycle_days,"onUpdate:modelValue":l[18]||(l[18]=o=>t.formData.dev_cycle_days=o),min:0,precision:0,style:{width:"380px","font-size":"14px"},placeholder:"请输入开发周期"},null,8,["modelValue"])]),_:1}),e(d,{label:"解决方案:"},{default:a(()=>[e(c,{modelValue:t.formData.solution,"onUpdate:modelValue":l[19]||(l[19]=o=>t.formData.solution=o),type:"textarea",autosize:{minRows:4,maxRows:10},style:{width:"600px","font-size":"14px"},placeholder:"请详细描述解决方案，支持多行文本",clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"解决人:"},{default:a(()=>[e(c,{modelValue:t.formData.solved_by,"onUpdate:modelValue":l[20]||(l[20]=o=>t.formData.solved_by=o),style:{width:"380px","font-size":"14px"},clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"解决日期:"},{default:a(()=>[e(y,{modelValue:t.formData.solved_time,"onUpdate:modelValue":l[21]||(l[21]=o=>t.formData.solved_time=o),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"380px","font-size":"14px"},placeholder:"选择日期"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),e(w,{modelValue:t.dialogVisible.delete,"onUpdate:modelValue":l[25]||(l[25]=o=>t.dialogVisible.delete=o),title:"删除需求问题记录",width:"500","align-center":""},{footer:a(()=>[p("div",null,[e(b,{onClick:l[24]||(l[24]=o=>t.dialogVisible.delete=!1)},{default:a(()=>[u("取消")]),_:1}),e(b,{type:"danger",onClick:m.submitDelete},{default:a(()=>[u("确认删除")]),_:1},8,["onClick"])])]),default:a(()=>[e(A,{type:"warning",title:`确定要删除需求问题描述为 ${t.formData.description} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),e(w,{modelValue:t.dialogVisible.detail,"onUpdate:modelValue":l[27]||(l[27]=o=>t.dialogVisible.detail=o),title:"需求问题详情",width:"850","align-center":""},{footer:a(()=>[p("div",ie,[e(b,{onClick:l[26]||(l[26]=o=>t.dialogVisible.detail=!1)},{default:a(()=>[u("关闭")]),_:1})])]),default:a(()=>[p("div",ae,[e(q,{column:1,border:""},{default:a(()=>[e(g,{label:"需求问题描述"},{default:a(()=>[p("div",oe,f(t.detailData.description),1)]),_:1}),t.detailData.solution?(h(),v(g,{key:0,label:"解决方案"},{default:a(()=>[p("div",se,f(t.detailData.solution),1)]),_:1})):x("",!0),e(g,{label:"提出日期"},{default:a(()=>[u(f(t.detailData.raised_time),1)]),_:1}),e(g,{label:"上报人"},{default:a(()=>[u(f(t.detailData.raised_by),1)]),_:1}),e(g,{label:"是否解决"},{default:a(()=>[e(Y,{type:t.detailData.is_solved==="已解决"?"success":"danger"},{default:a(()=>[u(f(t.detailData.is_solved),1)]),_:1},8,["type"])]),_:1}),t.detailData.solution_type?(h(),v(g,{key:1,label:"方案类型"},{default:a(()=>[e(Y,{type:"info"},{default:a(()=>[u(f(t.detailData.solution_type),1)]),_:1})]),_:1})):x("",!0),t.detailData.planned_dev_time?(h(),v(g,{key:2,label:"计划开发日期"},{default:a(()=>[u(f(t.detailData.planned_dev_time),1)]),_:1})):x("",!0),t.detailData.dev_cycle_days?(h(),v(g,{key:3,label:"开发周期(人天)"},{default:a(()=>[u(f(t.detailData.dev_cycle_days),1)]),_:1})):x("",!0),t.detailData.solved_by?(h(),v(g,{key:4,label:"解决人"},{default:a(()=>[u(f(t.detailData.solved_by),1)]),_:1})):x("",!0),t.detailData.solved_time?(h(),v(g,{key:5,label:"解决日期"},{default:a(()=>[u(f(t.detailData.solved_time),1)]),_:1})):x("",!0)]),_:1})])]),_:1},8,["modelValue"]),e(I,{class:"search-card"},{default:a(()=>[e(k,{inline:!0},{default:a(()=>[e(C,{gutter:10},{default:a(()=>[e(V,{span:6},{default:a(()=>[e(d,{label:"需求问题描述"},{default:a(()=>[e(c,{modelValue:t.search.description,"onUpdate:modelValue":l[28]||(l[28]=o=>t.search.description=o),placeholder:"请输入需求问题描述关键词",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(V,{span:6},{default:a(()=>[e(d,{label:"提出日期"},{default:a(()=>[e(y,{modelValue:t.search.raised_time,"onUpdate:modelValue":l[29]||(l[29]=o=>t.search.raised_time=o),type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(V,{span:6},{default:a(()=>[e(d,{label:"上报人"},{default:a(()=>[e(c,{modelValue:t.search.raised_by,"onUpdate:modelValue":l[30]||(l[30]=o=>t.search.raised_by=o),placeholder:"请输入上报人",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(V,{span:6},{default:a(()=>[e(d,{label:"是否解决"},{default:a(()=>[e(D,{modelValue:t.search.is_solved,"onUpdate:modelValue":l[31]||(l[31]=o=>t.search.is_solved=o),placeholder:"请选择",clearable:"",class:"form-control"},{default:a(()=>[e(r,{label:"所有",value:""}),e(r,{label:"已解决",value:"已解决"}),e(r,{label:"未解决",value:"未解决"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(C,{gutter:10},{default:a(()=>[e(V,{span:6},{default:a(()=>[e(d,{label:"方案类型"},{default:a(()=>[e(D,{modelValue:t.search.solution_type,"onUpdate:modelValue":l[32]||(l[32]=o=>t.search.solution_type=o),placeholder:"请选择",clearable:"",class:"form-control"},{default:a(()=>[e(r,{label:"所有",value:""}),e(r,{label:"新需求开发",value:"新需求开发"}),e(r,{label:"代码优化",value:"代码优化"}),e(r,{label:"BUG修复",value:"BUG修复"}),e(r,{label:"数据处理",value:"数据处理"}),e(r,{label:"不做任何处理",value:"不做任何处理"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(V,{span:18})]),_:1}),e(C,{gutter:10},{default:a(()=>[e(V,{span:24,class:"search-buttons-col"},{default:a(()=>[e(d,{class:"search-buttons"},{default:a(()=>[p("div",de,[e(b,{type:"primary",onClick:m.loadData},{default:a(()=>[e(z,null,{default:a(()=>[e(R)]),_:1}),u("查询 ")]),_:1},8,["onClick"]),e(b,{onClick:m.resetSearch},{default:a(()=>[u("重置")]),_:1},8,["onClick"]),e(b,{type:"success",disabled:!t.hasInsertPermission,onClick:l[33]||(l[33]=o=>t.hasInsertPermission?m.handleAdd():s.$message.warning("您没有新增权限"))},{default:a(()=>[e(z,null,{default:a(()=>[e(E)]),_:1}),u("新增 ")]),_:1},8,["disabled"]),e(b,{type:"info",onClick:m.exportData},{default:a(()=>[e(z,null,{default:a(()=>[e(F)]),_:1}),u("导出 ")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(I,{class:"table-card"},{default:a(()=>[K((h(),v(G,{data:t.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:m.handleSortChange},{default:a(()=>[x("",!0),e(_,{prop:"description",label:"需求问题描述",sortable:"","min-width":"400"},{default:a(o=>[p("div",{class:"description-text",onClick:U=>m.showDetail(o.row),title:"点击查看详情"},f(o.row.description),9,re)]),_:1}),e(_,{prop:"raised_time",label:"提出日期",sortable:""}),e(_,{prop:"raised_by",label:"上报人",sortable:""}),e(_,{prop:"is_solved",label:"是否解决",sortable:""},{default:a(o=>[e(Y,{type:o.row.is_solved==="已解决"?"success":"danger"},{default:a(()=>[u(f(o.row.is_solved),1)]),_:2},1032,["type"])]),_:1}),e(_,{prop:"solution_type",label:"方案类型",sortable:""},{default:a(o=>[o.row.solution_type?(h(),v(Y,{key:0,type:"info"},{default:a(()=>[u(f(o.row.solution_type),1)]),_:2},1024)):(h(),B("span",ne,"-"))]),_:1}),e(_,{prop:"planned_dev_time",label:"计划开发日期",sortable:""}),e(_,{prop:"dev_cycle_days",label:"开发周期(人天)",sortable:""}),e(_,{prop:"solution",label:"解决方案",sortable:""},{default:a(o=>[p("div",{class:"truncate-text",onClick:U=>m.showDetail(o.row),title:"点击查看详情"},f(o.row.solution),9,ue)]),_:1}),e(_,{prop:"solved_by",label:"解决人",sortable:""}),e(_,{prop:"solved_time",label:"解决日期",sortable:""}),e(_,{prop:"created_at",label:"创建时间",sortable:""}),e(_,{prop:"created_by",label:"创建人",sortable:""}),e(_,{prop:"updated_at",label:"更新时间",sortable:""}),e(_,{prop:"updated_by",label:"更新人",sortable:""}),e(_,{label:"操作",fixed:"right",width:"150"},{default:a(o=>[p("div",me,[e(b,{size:"small",type:"warning",disabled:!t.hasUpdatePermission,onClick:U=>m.handleEdit(o.$index,o.row)},{default:a(()=>[u("编辑")]),_:2},1032,["disabled","onClick"]),e(b,{size:"small",type:"danger",disabled:!t.hasDeletePermission,onClick:U=>m.handleDelete(o.$index,o.row)},{default:a(()=>[u("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[T,t.loading]]),p("div",_e,[e(N,{background:"","current-page":t.search.currentPage,"page-size":t.search.pageSize,total:t.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:m.handlePageSizeChange,onCurrentChange:m.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const be=O(X,[["render",pe],["__scopeId","data-v-c09764c1"]]);export{be as default};
