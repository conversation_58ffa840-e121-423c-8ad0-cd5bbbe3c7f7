import{_ as Ce,a1 as De,a3 as Fe,U as ke,a5 as Ue,z as Se,a4 as Ve,v as Ee,c as Re,a as t,f as h,w as n,b as p,y as ze,h as d,B as Le,C as Be,m as Z,t as k,x as We,r as g,J as E,o as Me,p as Ne,q as Oe,E as r,O as G,e as H}from"./index-C0WPPkX4.js";import{a as U,g as Te,s as L}from"./request-C6n2TFnY.js";const Pe={name:"OpsChangeTemplates",components:{Search:Ee,Upload:Ve,Download:Se,Document:Ue,Delete:ke,Refresh:Fe,Loading:De},setup(){var J;const b=g(null),i=E({keyword:"",sortProp:"updated_at",sortOrder:"desc"}),O=g([]),o=g(!1),T=U(),B=g(T==="admin"||((J=localStorage.getItem("role_code"))==null?void 0:J.includes("D"))||!1),D=()=>{const e=localStorage.getItem("role_code");console.log("当前用户角色权限:",e),B.value=T==="admin"||(e?e.includes("D"):!1),console.log("删除权限状态:",B.value)},m=E({currentPage:1,pageSize:10,total:0}),F=g(!1),W=g(null),c=E({templateName:"",templateDescription:"",file:null}),f={templateName:[{required:!0,message:"请输入模板名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],file:[{required:!0,message:"请选择模板文件",trigger:"change"}]},P=g(null),S=g([]),x=g(!1),M="/api/upload_ops_change_template?fileType=change_template",R={Authorization:`Bearer ${Te()}`},_=E({usernameby:U()||"admin",username:U()||"admin"});console.log("当前登录用户:",_.username);const N=g(null),V=g(!1),z=g(null),u=E({templateDescription:"",file:null,templateId:null}),I={file:[{required:!0,message:"请选择模板文件",trigger:"change"}]},q=g(null),s=g([]),y=g(!1),Q="/api/overwrite_ops_change_template",K=E({usernameby:U()||"admin",username:U()||"admin"}),w=async()=>{o.value=!0;try{const e={...i,currentPage:m.currentPage,pageSize:m.pageSize},a=await L({url:"/api/get_ops_change_templates",method:"post",data:e});a.code===0?(O.value=a.msg,m.total=a.total):r.error(`获取列表失败: ${a.msg}`)}catch(e){console.error("获取模板列表失败:",e),r.error("获取模板列表失败")}finally{o.value=!1}},X=({prop:e,order:a})=>{i.sortProp=e,i.sortOrder=a==="ascending"?"asc":"desc",w()},$=()=>{m.currentPage=1,w()},ee=()=>{i.keyword="",m.currentPage=1,b.value&&b.value.resetFields(),w(),r.success("搜索条件已重置")},oe=e=>{m.pageSize=e,w()},ae=e=>{m.currentPage=e,w()},te=()=>{w(),r.success("列表已刷新")},le=()=>{c.templateName="",c.templateDescription="",c.file=null,S.value=[],F.value=!0},ne=()=>{r.warning("只能上传一个文件")},re=e=>{c.file=e.raw},se=e=>{const a=e.name.toLowerCase();return a.endsWith(".doc")||a.endsWith(".docx")||a.endsWith(".xls")||a.endsWith(".xlsx")?e.type==="application/vnd.openxmlformats-officedocument.wordprocessingml.document"||e.type==="application/msword"||e.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||e.type==="application/vnd.ms-excel"?e.size/1024/1024<20?!0:(r({message:"文件大小不能超过20MB",type:"warning",duration:5e3,showClose:!0}),!1):(r({message:"文件类型不符合要求，只能上传Word或Excel文件",type:"warning",duration:5e3,showClose:!0}),!1):(r({message:"只能上传Word或Excel文件（.doc, .docx, .xls, .xlsx）",type:"warning",duration:5e3,showClose:!0}),!1)},A=e=>{if(!e)return!1;const a=e.name.toLowerCase();return a.endsWith(".doc")||a.endsWith(".docx")||a.endsWith(".xls")||a.endsWith(".xlsx")?e.type==="application/vnd.openxmlformats-officedocument.wordprocessingml.document"||e.type==="application/msword"||e.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||e.type==="application/vnd.ms-excel"?e.size/1024/1024<20?!0:(r({message:"文件大小不能超过20MB",type:"warning",duration:5e3,showClose:!0}),!1):(r({message:"文件类型不符合要求，只能上传Word或Excel文件",type:"warning",duration:5e3,showClose:!0}),!1):(r({message:"只能上传Word或Excel文件（.doc, .docx, .xls, .xlsx）",type:"warning",duration:5e3,showClose:!0}),!1)},ie=()=>{W.value.validate(async e=>{if(e){if(!c.file){r({message:"请选择模板文件",type:"warning",duration:5e3,showClose:!0});return}if(!c.file||!(c.file instanceof File)){r({message:"文件对象无效，请重新选择文件",type:"warning",duration:5e3,showClose:!0});return}if(!A(c.file))return;x.value=!0;const a=new FormData;a.append("file",c.file),a.append("templateName",c.templateName),a.append("templateDescription",c.templateDescription);try{const l=c.file.name||"未命名文件";a.append("originalFilename",l),console.log("添加原始文件名:",l)}catch(l){console.error("获取文件名失败:",l),a.append("originalFilename","未命名文件")}a.append("usernameby",_.usernameby),a.append("username",_.username),a.append("fileType","change_template");try{console.log("开始上传文件...");const l=await L({url:M,method:"post",data:a,headers:{...R,"Content-Type":"multipart/form-data"},timeout:6e4,onUploadProgress:v=>{const C=Math.round(v.loaded*100/v.total);console.log(`上传进度: ${C}%`)}});l.code===0?(r.success("模板上传成功"),F.value=!1,w()):r({message:`上传失败: ${l.msg}`,type:"warning",duration:5e3,showClose:!0})}catch(l){console.error("上传模板失败:",l),l.response&&l.response.status===400&&l.response.data&&l.response.data.msg?r({message:l.response.data.msg,type:"warning",duration:5e3,showClose:!0}):r({message:"上传模板失败，请确保文件格式正确（仅支持.doc、.docx、.xls、.xlsx）",type:"warning",duration:5e3,showClose:!0})}finally{x.value=!1}}else return!1})},de=e=>{e.code===0?(r.success("模板上传成功"),F.value=!1,w()):r.error(`上传失败: ${e.msg}`),x.value=!1},ce=e=>{console.error("上传错误:",e),e.response&&e.response.status===400&&e.response.data&&e.response.data.msg?r({message:e.response.data.msg,type:"warning",duration:5e3,showClose:!0}):r({message:"上传失败，请确保文件格式正确（仅支持.doc、.docx、.xls、.xlsx）",type:"warning",duration:5e3,showClose:!0}),x.value=!1},pe=e=>{N.value=e,u.templateDescription=e.template_description||"",u.templateId=e.id,s.value=[],u.file=null,V.value=!0},me=e=>{u.file=e.raw},ue=e=>{e.code===0?(r.success("模板覆盖上传成功"),V.value=!1,w()):r.error(`覆盖上传失败: ${e.msg}`),y.value=!1},fe=()=>{z.value.validate(async e=>{if(e){if(!u.file){r({message:"请选择模板文件",type:"warning",duration:5e3,showClose:!0});return}if(!u.file||!(u.file instanceof File)){r({message:"文件对象无效，请重新选择文件",type:"warning",duration:5e3,showClose:!0});return}if(!A(u.file))return;G.confirm("此操作将覆盖现有模板文件，是否继续？","覆盖上传确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{y.value=!0;const a=new FormData;a.append("file",u.file),a.append("templateId",u.templateId),a.append("templateDescription",u.templateDescription);try{const l=u.file.name||"未命名文件";a.append("originalFilename",l),console.log("添加原始文件名:",l)}catch(l){console.error("获取文件名失败:",l),a.append("originalFilename","未命名文件")}a.append("usernameby",K.usernameby),a.append("username",K.username);try{console.log("开始覆盖上传文件...");const l=await L({url:Q,method:"post",data:a,headers:{...R,"Content-Type":"multipart/form-data"},timeout:6e4,onUploadProgress:v=>{const C=Math.round(v.loaded*100/v.total);console.log(`覆盖上传进度: ${C}%`)}});l.code===0?(r.success("模板覆盖上传成功"),V.value=!1,w()):r({message:`覆盖上传失败: ${l.msg}`,type:"warning",duration:5e3,showClose:!0})}catch(l){console.error("覆盖上传模板失败:",l),l.response&&l.response.status===400&&l.response.data&&l.response.data.msg?r({message:l.response.data.msg,type:"warning",duration:5e3,showClose:!0}):r({message:"覆盖上传模板失败，请确保文件格式正确（仅支持.doc、.docx、.xls、.xlsx）",type:"warning",duration:5e3,showClose:!0})}finally{y.value=!1}}).catch(()=>{})}else return!1})},ge=async e=>{try{const a=await L({url:`/api/download_ops_change_template?id=${e.id}`,method:"get"});if(a.code===0){if(a.msg.directDownloadUrl){console.log("使用直接下载URL:",a.msg.directDownloadUrl);const l=document.createElement("a");l.href=a.msg.directDownloadUrl,l.target="_blank",document.body.appendChild(l),l.click(),document.body.removeChild(l)}else{console.log("使用文件URL:",a.msg.url);const l=document.createElement("a");l.href=a.msg.url,l.target="_blank",l.download=a.msg.filename,document.body.appendChild(l),l.click(),document.body.removeChild(l)}r.success("文件下载成功")}else r.error(`下载失败: ${a.msg}`)}catch(a){console.error("下载模板失败:",a),r.error("下载模板失败")}},he=e=>{G.confirm(`确定要删除模板 "${e.template_name}" 吗？此操作不可恢复。`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const a=await L({url:"/api/delete_ops_change_template",method:"post",data:{id:e.id,usernameby:U()||"admin",username:U()||"admin"}});a.code===0?(r.success("删除成功"),w()):r.error(`删除失败: ${a.msg}`)}catch(a){console.error("删除模板失败:",a),r.error("删除模板失败")}}).catch(()=>{})},_e=e=>e<1024?e+" B":e<1024*1024?(e/1024).toFixed(2)+" KB":(e/(1024*1024)).toFixed(2)+" MB",we=e=>{if(!e)return"";try{const a=new Date(e);if(isNaN(a.getTime()))return e;const l=xe=>String(xe).padStart(2,"0"),v=a.getFullYear(),C=l(a.getMonth()+1),Y=l(a.getDate()),ye=l(a.getHours()),ve=l(a.getMinutes()),be=l(a.getSeconds());return a.getHours()===0&&a.getMinutes()===0&&a.getSeconds()===0?`${v}-${C}-${Y}`:`${v}-${C}-${Y} ${ye}:${ve}:${be}`}catch(a){return console.error("日期格式化错误:",a),e}};return Me(()=>{w(),D()}),{searchFormRef:b,searchForm:i,tableData:O,tableLoading:o,pagination:m,uploadDialogVisible:F,uploadFormRef:W,uploadForm:c,uploadRules:f,uploadRef:P,fileList:S,uploadLoading:x,uploadUrl:M,headers:R,uploadData:_,currentTemplate:N,overwriteDialogVisible:V,overwriteFormRef:z,overwriteForm:u,overwriteRules:I,overwriteUploadRef:q,overwriteFileList:s,overwriteLoading:y,overwriteUploadData:K,hasDeletePermission:B,formatFileSize:_e,formatDateTime:we,handleSearch:$,resetSearch:ee,handleSizeChange:oe,handleCurrentChange:ae,handleSortChange:X,handleRefresh:te,handleUpload:le,handleExceed:ne,handleFileChange:re,beforeUpload:se,submitUpload:ie,handleUploadSuccess:de,handleUploadError:ce,handleOverwrite:pe,handleOverwriteFileChange:me,handleOverwriteSuccess:ue,submitOverwrite:fe,handleDownload:ge,handleDelete:he,checkPermissions:D}}},j=b=>(Ne("data-v-8e197758"),b=b(),Oe(),b),Ie={class:"app-container"},qe={class:"button-container"},Ke={class:"action-bar unified-action-bar"},He={class:"action-bar-left"},je={class:"action-bar-right"},Ae={style:{display:"flex","white-space":"nowrap"}},Je={class:"pagination"},Ye=j(()=>h("div",{class:"el-upload__tip"}," 只能上传Word或Excel文件，且不超过20MB ",-1)),Ze={class:"dialog-footer"},Ge=j(()=>h("p",null,"您正在覆盖上传模板文件，此操作将替换现有文件，请确认！",-1)),Qe=j(()=>h("div",{class:"el-upload__tip"}," 只能上传Word或Excel文件，且不超过20MB ",-1)),Xe={class:"dialog-footer"};function $e(b,i,O,o,T,B){const D=p("el-input"),m=p("el-form-item"),F=p("el-col"),W=p("Search"),c=p("el-icon"),f=p("el-button"),P=p("el-row"),S=p("el-form"),x=p("el-card"),M=p("Upload"),R=p("Refresh"),_=p("el-table-column"),N=p("el-table"),V=p("el-pagination"),z=p("el-upload"),u=p("el-dialog"),I=p("el-alert"),q=Be("loading");return H(),Re("div",Ie,[t(x,{class:"search-card"},{default:n(()=>[t(S,{model:o.searchForm,ref:"searchFormRef","label-width":"100px","label-position":"right"},{default:n(()=>[t(P,{gutter:20},{default:n(()=>[t(F,{xs:24,sm:12,md:8,lg:6},{default:n(()=>[t(m,{label:"关键字"},{default:n(()=>[t(D,{modelValue:o.searchForm.keyword,"onUpdate:modelValue":i[0]||(i[0]=s=>o.searchForm.keyword=s),placeholder:"模板名称/描述",clearable:"",onKeyup:ze(o.handleSearch,["enter"]),class:"form-control"},null,8,["modelValue","onKeyup"])]),_:1})]),_:1}),t(F,{xs:24,sm:12,md:8,lg:18,class:"search-buttons-col"},{default:n(()=>[t(m,null,{default:n(()=>[h("div",qe,[t(f,{type:"primary",onClick:o.handleSearch},{default:n(()=>[t(c,null,{default:n(()=>[t(W)]),_:1}),d("查询 ")]),_:1},8,["onClick"]),t(f,{onClick:o.resetSearch},{default:n(()=>[d("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),h("div",Ke,[h("div",He,[t(f,{type:"success",onClick:o.handleUpload},{default:n(()=>[t(c,null,{default:n(()=>[t(M)]),_:1}),d("上传模板 ")]),_:1},8,["onClick"])]),h("div",je,[t(f,{type:"primary",onClick:o.handleRefresh},{default:n(()=>[t(c,null,{default:n(()=>[t(R)]),_:1}),d("刷新 ")]),_:1},8,["onClick"])])]),t(x,{class:"table-card"},{default:n(()=>[Le((H(),Z(N,{data:o.tableData,border:"",stripe:"",style:{width:"100%"},"table-layout":"auto","header-cell-style":{background:"#f5f7fa",color:"#606266"},onSortChange:o.handleSortChange},{default:n(()=>[t(_,{prop:"template_name",label:"模板名称","min-width":"150","show-overflow-tooltip":"",sortable:""}),t(_,{prop:"template_description",label:"模板描述","min-width":"200","show-overflow-tooltip":"",sortable:""}),t(_,{prop:"original_filename",label:"原始文件名","min-width":"200","show-overflow-tooltip":"",sortable:""}),t(_,{prop:"file_size",label:"文件大小","min-width":"120",sortable:""},{default:n(s=>[d(k(o.formatFileSize(s.row.file_size)),1)]),_:1}),t(_,{label:"创建时间","min-width":"150",sortable:"created_at"},{default:n(s=>[d(k(o.formatDateTime(s.row.created_at)),1)]),_:1}),t(_,{label:"创建人","min-width":"100",sortable:"created_by"},{default:n(s=>[d(k(s.row.created_by),1)]),_:1}),t(_,{label:"更新时间","min-width":"150",sortable:"updated_at"},{default:n(s=>[d(k(o.formatDateTime(s.row.updated_at)),1)]),_:1}),t(_,{label:"更新人","min-width":"100",sortable:"updated_by"},{default:n(s=>[d(k(s.row.updated_by),1)]),_:1}),t(_,{label:"操作",fixed:"right","min-width":"200"},{default:n(s=>[h("div",Ae,[t(f,{type:"success",size:"small",onClick:y=>o.handleDownload(s.row)},{default:n(()=>[d(" 下载 ")]),_:2},1032,["onClick"]),t(f,{type:"warning",size:"small",onClick:y=>o.handleOverwrite(s.row)},{default:n(()=>[d(" 覆盖上传 ")]),_:2},1032,["onClick"]),o.hasDeletePermission?(H(),Z(f,{key:0,type:"danger",size:"small",onClick:y=>o.handleDelete(s.row)},{default:n(()=>[d(" 删除 ")]),_:2},1032,["onClick"])):We("",!0)])]),_:1})]),_:1},8,["data","onSortChange"])),[[q,o.tableLoading]]),h("div",Je,[t(V,{background:"","current-page":o.pagination.currentPage,"page-size":o.pagination.pageSize,total:o.pagination.total,"page-sizes":[10,20,50,100],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:o.handleSizeChange,onCurrentChange:o.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),t(u,{modelValue:o.uploadDialogVisible,"onUpdate:modelValue":i[4]||(i[4]=s=>o.uploadDialogVisible=s),title:"上传变更模板",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:n(()=>[h("span",Ze,[t(f,{onClick:i[3]||(i[3]=s=>o.uploadDialogVisible=!1)},{default:n(()=>[d("取消")]),_:1}),t(f,{type:"primary",onClick:o.submitUpload,loading:o.uploadLoading},{default:n(()=>[d(" 上传 ")]),_:1},8,["onClick","loading"])])]),default:n(()=>[t(S,{model:o.uploadForm,ref:"uploadFormRef","label-width":"100px",rules:o.uploadRules},{default:n(()=>[t(m,{label:"模板名称",prop:"templateName"},{default:n(()=>[t(D,{modelValue:o.uploadForm.templateName,"onUpdate:modelValue":i[1]||(i[1]=s=>o.uploadForm.templateName=s),placeholder:"请输入模板名称"},null,8,["modelValue"])]),_:1}),t(m,{label:"模板描述",prop:"templateDescription"},{default:n(()=>[t(D,{modelValue:o.uploadForm.templateDescription,"onUpdate:modelValue":i[2]||(i[2]=s=>o.uploadForm.templateDescription=s),type:"textarea",rows:3,placeholder:"请输入模板描述"},null,8,["modelValue"])]),_:1}),t(m,{label:"模板文件",prop:"file"},{default:n(()=>[t(z,{ref:"uploadRef",class:"template-upload",action:o.uploadUrl,headers:o.headers,data:o.uploadData,"auto-upload":!1,limit:1,"on-exceed":o.handleExceed,"on-change":o.handleFileChange,"on-success":o.handleUploadSuccess,"on-error":o.handleUploadError,"before-upload":o.beforeUpload,"file-list":o.fileList,accept:".doc,.docx,.xls,.xlsx"},{trigger:n(()=>[t(f,{type:"primary"},{default:n(()=>[d("选择文件")]),_:1})]),tip:n(()=>[Ye]),_:1},8,["action","headers","data","on-exceed","on-change","on-success","on-error","before-upload","file-list"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),t(u,{modelValue:o.overwriteDialogVisible,"onUpdate:modelValue":i[7]||(i[7]=s=>o.overwriteDialogVisible=s),title:"覆盖上传模板",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:n(()=>[h("span",Xe,[t(f,{onClick:i[6]||(i[6]=s=>o.overwriteDialogVisible=!1)},{default:n(()=>[d("取消")]),_:1}),t(f,{type:"warning",onClick:o.submitOverwrite,loading:o.overwriteLoading},{default:n(()=>[d(" 覆盖上传 ")]),_:1},8,["onClick","loading"])])]),default:n(()=>[t(I,{type:"warning",closable:!1,"show-icon":"",style:{"margin-bottom":"20px"}},{default:n(()=>{var s,y;return[Ge,h("p",null,[d("模板名称: "),h("strong",null,k((s=o.currentTemplate)==null?void 0:s.template_name),1)]),h("p",null,[d("原始文件名: "),h("strong",null,k((y=o.currentTemplate)==null?void 0:y.original_filename),1)])]}),_:1}),t(S,{model:o.overwriteForm,ref:"overwriteFormRef","label-width":"100px",rules:o.overwriteRules},{default:n(()=>[t(m,{label:"模板描述",prop:"templateDescription"},{default:n(()=>[t(D,{modelValue:o.overwriteForm.templateDescription,"onUpdate:modelValue":i[5]||(i[5]=s=>o.overwriteForm.templateDescription=s),type:"textarea",rows:3,placeholder:"请输入模板描述（可选）"},null,8,["modelValue"])]),_:1}),t(m,{label:"模板文件",prop:"file"},{default:n(()=>[t(z,{ref:"overwriteUploadRef",class:"template-upload",action:b.overwriteUploadUrl,headers:o.headers,data:o.overwriteUploadData,"auto-upload":!1,limit:1,"on-exceed":o.handleExceed,"on-change":o.handleOverwriteFileChange,"on-success":o.handleOverwriteSuccess,"on-error":o.handleUploadError,"before-upload":o.beforeUpload,"file-list":o.overwriteFileList,accept:".doc,.docx,.xls,.xlsx"},{trigger:n(()=>[t(f,{type:"primary"},{default:n(()=>[d("选择文件")]),_:1})]),tip:n(()=>[Qe]),_:1},8,["action","headers","data","on-exceed","on-change","on-success","on-error","before-upload","file-list"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}const ao=Ce(Pe,[["render",$e],["__scopeId","data-v-8e197758"]]);export{ao as default};
