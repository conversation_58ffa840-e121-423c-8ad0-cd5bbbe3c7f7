import{_ as N,z as O,v as H,A as j,c,a as e,f as v,w as o,b as f,F as h,l as V,h as b,B as K,C as L,m as g,x as G,t as x,e as i}from"./index-Bo9zKuAo.js";import{u as Y,w as J,F as Q}from"./FileSaver.min-BlIK9FxR.js";const W={components:{Plus:j,Search:H,Download:O},data(){var u,a,p;return{userArr:[],loading:!1,servertypes:[],productionattributes:[],datacenters:[],operationstatus:[],operatingsystems:[],hasDeletePermission:(u=localStorage.getItem("role_code"))==null?void 0:u.includes("D"),hasUpdatePermission:(a=localStorage.getItem("role_code"))==null?void 0:a.includes("U"),hasInsertPermission:(p=localStorage.getItem("role_code"))==null?void 0:p.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{management_ip:"",hostname:"",admin1:"",admin2:"",server_type:"",production_attributes:"",data_center:"",out_of_band_management_ilo:"",operation_status:"",is_monitored:"",online_status:"",os_category:"",year_category:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:"",management_ip:"",hostname:"",function_purpose:"",admin1:"",admin2:"",server_type:"",production_attributes:"",data_center:"",out_of_band_management_ilo:"",operation_status:"",is_innovative_tech:"",asset_number:"",purchase_date:"",maintenance_years:"",maintenance_end_date:"",serial_number:"",server_model:"",is_monitored:"",cpu_model:"",memory:"",disk:"",network_card:"",operating_system:"",os_category:"",remarks:"",year_category:"",weak_password_exists:"",weak_password_correction_date:"",is_single_point:"",managed_addresses:""},rules:{management_ip:[{required:!0,message:"请输入管理IP",trigger:"blur"},{pattern:/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,message:"请输入正确的IP地址格式",trigger:"blur"}],hostname:[{required:!0,message:"请输入主机名",trigger:"blur"},{min:2,max:50,message:"主机名长度应在2-50个字符之间",trigger:"blur"}],function_purpose:[{required:!0,message:"请输入功能用途",trigger:"blur"}],admin1:[{required:!0,message:"请输入管理员1",trigger:"blur"}],server_type:[{required:!0,message:"请选择服务器类型",trigger:"change"}],production_attributes:[{required:!0,message:"请选择生产属性",trigger:"change"}],operation_status:[{required:!0,message:"请选择生命周期",trigger:"change"}],data_center:[{required:!0,message:"请选择所属机房",trigger:"change"}],is_innovative_tech:[{required:!0,message:"请选择是否信创",trigger:"change"}],managed_addresses:[{required:!1,validator:(U,t,_)=>{if(this.formData.is_single_point==="否")if(!t)_(new Error("当服务器不是单点时，互备主机IP为必填项"));else{const s=t.split(","),r=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;s.filter(m=>!r.test(m.trim())).length>0?_(new Error("请输入正确的IP地址格式，多个IP请用英文逗号分隔")):_()}else _()},trigger:"blur"}]}}},mounted(){this.$route.query.search_ip&&(this.search.management_ip=this.$route.query.search_ip),this.loadData(),this.getDatadict("E","servertypes"),this.getDatadict("C","productionattributes"),this.getDatadict("A","datacenters"),this.getDatadict("D","operationstatus"),this.getDatadict("K","operatingsystems"),this.$route.query.from_discovery==="true"&&this.$nextTick(()=>{this.handleAddFromDiscovery()})},methods:{handlePageChange(u){this.search.currentPage=u,this.loadData()},handlePageSizeChange(u){this.search.pageSize=parseInt(u),this.search.currentPage=1,this.loadData()},handleSortChange({column:u,prop:a,order:p}){this.search.sortProp=a,this.search.sortOrder=p==="ascending"?"asc":"desc",this.loadData()},async loadData(){try{this.loading=!0;const u=await this.$axios.post("/api/get_cmdb_server_management",this.search);this.userArr=u.data.msg,this.search.total=u.data.total}catch(u){console.error("数据加载失败:",u),this.$message.error("数据加载失败")}finally{this.loading=!1}},async validateAndSubmitAdd(){try{await this.$refs.addFormRef.validate(),await this.submitAdd()}catch{this.$message.error("请完善必填项后再提交")}},async submitAdd(){var u,a;try{const p={...this.formData,usernameby:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/add_cmdb_server_management",p),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(p){console.error("添加失败:",p),this.$message.error(((a=(u=p.response)==null?void 0:u.data)==null?void 0:a.msg)||"添加失败")}},async validateAndSubmitEdit(){try{await this.$refs.editFormRef.validate(),await this.submitEdit()}catch{this.$message.error("请完善必填项后再提交")}},async submitEdit(){var u,a;try{const p={...this.formData,usernameby:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/update_cmdb_server_management",p),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(p){console.error("更新失败:",p),this.$message.error(((a=(u=p.response)==null?void 0:u.data)==null?void 0:a.msg)||"更新失败")}},async submitDelete(u){try{await this.$axios.post("/api/del_cmdb_server_management",this.formData),this.$message.success("删除成功"),this.loadData(),this.dialogVisible.delete=!1}catch(a){console.error("删除失败:",a),this.$message.error("删除失败")}},resetSearch(){this.search={management_ip:"",hostname:"",admin1:"",admin2:"",server_type:"",production_attributes:"",data_center:"",out_of_band_management_ilo:"",operation_status:"",is_innovative_tech:"",is_monitored:"",online_status:"",os_category:"",year_category:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},async getDatadict(u,a){try{const p=await this.$axios.post("/api/get_cmdb_data_dictionary",{dict_code:u});this[a]=p.data.msg}catch(p){console.error("数据加载失败:",p),this.$message.error("数据加载失败")}},handleAdd(){this.dialogVisible.add=!this.dialogVisible.add,this.formData={weak_password_exists:"否",is_single_point:"否",is_innovative_tech:"否",is_monitored:"否",managed_addresses:""},this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields(),this.validateManagedAddresses()})},handleEdit(u,a){this.dialogVisible.edit=!0,this.formData.id=a.id,this.formData.management_ip=a.management_ip,this.formData.hostname=a.hostname,this.formData.function_purpose=a.function_purpose,this.formData.admin1=a.admin1,this.formData.admin2=a.admin2,this.formData.server_type=a.server_type,this.formData.production_attributes=a.production_attributes,this.formData.data_center=a.data_center,this.formData.out_of_band_management_ilo=a.out_of_band_management_ilo,this.formData.operation_status=a.operation_status,this.formData.is_innovative_tech=a.is_innovative_tech,this.formData.asset_number=a.asset_number,this.formData.purchase_date=a.purchase_date,this.formData.maintenance_years=a.maintenance_years,this.formData.maintenance_end_date=a.maintenance_end_date,this.formData.serial_number=a.serial_number,this.formData.server_model=a.server_model,this.formData.is_monitored=a.is_monitored,this.formData.cpu_model=a.cpu_model,this.formData.memory=a.memory,this.formData.disk=a.disk,this.formData.network_card=a.network_card,this.formData.operating_system=a.operating_system,this.formData.os_category=a.os_category,this.formData.remarks=a.remarks,this.formData.year_category=a.year_category,this.formData.weak_password_exists=a.weak_password_exists,this.formData.weak_password_correction_date=a.weak_password_correction_date,this.formData.is_single_point=a.is_single_point,this.formData.managed_addresses=a.managed_addresses,console.log("编辑表单数据:",{is_innovative_tech:this.formData.is_innovative_tech,operating_system:this.formData.operating_system}),this.$refs.editFormRef&&this.$nextTick(()=>{this.$refs.editFormRef.clearValidate()})},handleDelete(u,a){this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=a.id,this.formData.management_ip=a.management_ip},exportData(){const a=this.$refs.table.columns,p=a.map(m=>m.label),U=this.userArr.map(m=>a.map(w=>m[w.property])),t=[p,...U],_=Y.aoa_to_sheet(t),s=Y.book_new();Y.book_append_sheet(s,_,"Sheet1");const r=J(s,{bookType:"xlsx",type:"array"}),d=new Blob([r],{type:"application/octet-stream"});Q.saveAs(d,"实体服务器.xlsx")},handleAddFromDiscovery(){this.dialogVisible.add=!0;const{ip_address:u,hostname:a,open_ports:p}=this.$route.query;this.formData={management_ip:u||"",hostname:a||"",function_purpose:"",admin1:"",admin2:"",server_type:"",production_attributes:"",data_center:"",operation_status:"",is_innovative_tech:"否",remarks:p?`开放端口: ${p}`:"",weak_password_exists:"否",is_single_point:"否",managed_addresses:"",is_monitored:"否"},this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields(),this.$message.info("请完善资产信息并提交")}),this.$router.replace({path:this.$route.path})},validateManagedAddresses(){this.$refs.addFormRef&&this.$refs.addFormRef.validateField("managed_addresses"),this.$refs.editFormRef&&this.$refs.editFormRef.validateField("managed_addresses"),this.formData.is_single_point==="是"&&(this.formData.managed_addresses="")}}},X={class:"user-manage"},Z={class:"dialogdiv"},$={class:"dialog-footer"},ee={class:"dialogdiv"},le={class:"dialog-footer"},ae={class:"button-container"},te={class:"action-bar unified-action-bar"},oe={class:"action-bar-left"},re={class:"action-bar-right"},se={style:{display:"flex","white-space":"nowrap"}},de={class:"pagination"};function ne(u,a,p,U,t,_){const s=f("el-input"),r=f("el-form-item"),d=f("el-option"),m=f("el-select"),w=f("el-date-picker"),P=f("el-form"),y=f("el-button"),C=f("el-dialog"),F=f("el-alert"),D=f("el-col"),A=f("Search"),I=f("el-icon"),M=f("el-row"),S=f("el-card"),q=f("Plus"),z=f("Download"),n=f("el-table-column"),k=f("el-tag"),R=f("el-table"),E=f("el-pagination"),B=L("loading");return i(),c("div",X,[e(C,{modelValue:t.dialogVisible.add,"onUpdate:modelValue":a[27]||(a[27]=l=>t.dialogVisible.add=l),title:"新增服务器信息",width:"400","align-center":""},{footer:o(()=>[v("div",$,[e(y,{onClick:a[26]||(a[26]=l=>t.dialogVisible.add=!1)},{default:o(()=>[b("返回")]),_:1}),e(y,{type:"primary",onClick:_.validateAndSubmitAdd},{default:o(()=>[b("确定")]),_:1},8,["onClick"])])]),default:o(()=>[v("div",Z,[e(P,{model:t.formData,rules:t.rules,ref:"addFormRef","label-position":"right","status-icon":""},{default:o(()=>[e(r,{prop:"management_ip",label:"管理IP:"},{default:o(()=>[e(s,{modelValue:t.formData.management_ip,"onUpdate:modelValue":a[0]||(a[0]=l=>t.formData.management_ip=l),style:{width:"240px"},clearable:"",placeholder:"请输入管理IP"},null,8,["modelValue"])]),_:1}),e(r,{prop:"hostname",label:"主机名:"},{default:o(()=>[e(s,{modelValue:t.formData.hostname,"onUpdate:modelValue":a[1]||(a[1]=l=>t.formData.hostname=l),style:{width:"240px"},clearable:"",placeholder:"请输入主机名"},null,8,["modelValue"])]),_:1}),e(r,{prop:"function_purpose",label:"功能用途:"},{default:o(()=>[e(s,{modelValue:t.formData.function_purpose,"onUpdate:modelValue":a[2]||(a[2]=l=>t.formData.function_purpose=l),style:{width:"240px"},clearable:"",placeholder:"请输入功能用途"},null,8,["modelValue"])]),_:1}),e(r,{prop:"admin1",label:"管理员1:"},{default:o(()=>[e(s,{modelValue:t.formData.admin1,"onUpdate:modelValue":a[3]||(a[3]=l=>t.formData.admin1=l),style:{width:"240px"},clearable:"",placeholder:"请输入管理员1"},null,8,["modelValue"])]),_:1}),e(r,{prop:"admin2",label:"管理员2:"},{default:o(()=>[e(s,{modelValue:t.formData.admin2,"onUpdate:modelValue":a[4]||(a[4]=l=>t.formData.admin2=l),style:{width:"240px"},clearable:"",placeholder:"请输入管理员2"},null,8,["modelValue"])]),_:1}),e(r,{prop:"server_type",label:"服务器类型:"},{default:o(()=>[e(m,{modelValue:t.formData.server_type,"onUpdate:modelValue":a[5]||(a[5]=l=>t.formData.server_type=l),style:{width:"240px"},placeholder:"请选择服务器类型"},{default:o(()=>[(i(!0),c(h,null,V(t.servertypes,l=>(i(),g(d,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"production_attributes",label:"生产属性:"},{default:o(()=>[e(m,{modelValue:t.formData.production_attributes,"onUpdate:modelValue":a[6]||(a[6]=l=>t.formData.production_attributes=l),style:{width:"240px"},placeholder:"请选择生产属性"},{default:o(()=>[(i(!0),c(h,null,V(t.productionattributes,l=>(i(),g(d,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"data_center",label:"所属机房:"},{default:o(()=>[e(m,{modelValue:t.formData.data_center,"onUpdate:modelValue":a[7]||(a[7]=l=>t.formData.data_center=l),style:{width:"240px"},placeholder:"请选择所属机房"},{default:o(()=>[(i(!0),c(h,null,V(t.datacenters,l=>(i(),g(d,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"out_of_band_management_ilo",label:"带外管理IP:"},{default:o(()=>[e(s,{modelValue:t.formData.out_of_band_management_ilo,"onUpdate:modelValue":a[8]||(a[8]=l=>t.formData.out_of_band_management_ilo=l),style:{width:"240px"},clearable:"",placeholder:"请输入带外管理IP"},null,8,["modelValue"])]),_:1}),e(r,{prop:"operation_status",label:"生命周期:"},{default:o(()=>[e(m,{modelValue:t.formData.operation_status,"onUpdate:modelValue":a[9]||(a[9]=l=>t.formData.operation_status=l),style:{width:"240px"},placeholder:"请选择生命周期"},{default:o(()=>[(i(!0),c(h,null,V(t.operationstatus,l=>(i(),g(d,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"is_innovative_tech",label:"是否信创:"},{default:o(()=>[e(m,{modelValue:t.formData.is_innovative_tech,"onUpdate:modelValue":a[10]||(a[10]=l=>t.formData.is_innovative_tech=l),style:{width:"240px"},placeholder:"请选择是否信创"},{default:o(()=>[e(d,{label:"是",value:"是"}),e(d,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"is_single_point",label:"是否单点:"},{default:o(()=>[e(m,{modelValue:t.formData.is_single_point,"onUpdate:modelValue":a[11]||(a[11]=l=>t.formData.is_single_point=l),style:{width:"240px"},placeholder:"请选择是否单点",onChange:_.validateManagedAddresses},{default:o(()=>[e(d,{label:"是",value:"是"}),e(d,{label:"否",value:"否"})]),_:1},8,["modelValue","onChange"])]),_:1}),e(r,{prop:"managed_addresses",label:"互备主机IP:",required:t.formData.is_single_point==="否"},{default:o(()=>[e(s,{modelValue:t.formData.managed_addresses,"onUpdate:modelValue":a[12]||(a[12]=l=>t.formData.managed_addresses=l),style:{width:"240px"},clearable:"",placeholder:"请输入互备主机IP，多个IP用英文逗号分隔"},null,8,["modelValue"])]),_:1},8,["required"]),e(r,{prop:"asset_number",label:"财务资产编号:"},{default:o(()=>[e(s,{modelValue:t.formData.asset_number,"onUpdate:modelValue":a[13]||(a[13]=l=>t.formData.asset_number=l),style:{width:"240px"},clearable:"",placeholder:"请输入财务资产编号"},null,8,["modelValue"])]),_:1}),e(r,{prop:"purchase_date",label:"采购时间:"},{default:o(()=>[e(w,{modelValue:t.formData.purchase_date,"onUpdate:modelValue":a[14]||(a[14]=l=>t.formData.purchase_date=l),type:"datetime","value-format":"YYYY-MM-DD","time-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY-MM-DD",style:{width:"240px"},placeholder:"选择日期"},null,8,["modelValue"])]),_:1}),e(r,{prop:"maintenance_years",label:"维保年限:"},{default:o(()=>[e(s,{modelValue:t.formData.maintenance_years,"onUpdate:modelValue":a[15]||(a[15]=l=>t.formData.maintenance_years=l),style:{width:"240px"},clearable:"",placeholder:"请输入维保年限"},null,8,["modelValue"])]),_:1}),e(r,{prop:"serial_number",label:"序列号:"},{default:o(()=>[e(s,{modelValue:t.formData.serial_number,"onUpdate:modelValue":a[16]||(a[16]=l=>t.formData.serial_number=l),style:{width:"240px"},clearable:"",placeholder:"请输入序列号"},null,8,["modelValue"])]),_:1}),e(r,{prop:"server_model",label:"服务器型号:"},{default:o(()=>[e(s,{modelValue:t.formData.server_model,"onUpdate:modelValue":a[17]||(a[17]=l=>t.formData.server_model=l),style:{width:"240px"},clearable:"",placeholder:"请输入服务器型号"},null,8,["modelValue"])]),_:1}),e(r,{prop:"cpu_model",label:"CPU型号:"},{default:o(()=>[e(s,{modelValue:t.formData.cpu_model,"onUpdate:modelValue":a[18]||(a[18]=l=>t.formData.cpu_model=l),style:{width:"240px"},clearable:"",placeholder:"请输入CPU型号"},null,8,["modelValue"])]),_:1}),e(r,{prop:"memory",label:"内存:"},{default:o(()=>[e(s,{modelValue:t.formData.memory,"onUpdate:modelValue":a[19]||(a[19]=l=>t.formData.memory=l),style:{width:"240px"},clearable:"",placeholder:"请输入内存"},null,8,["modelValue"])]),_:1}),e(r,{prop:"disk",label:"硬盘:"},{default:o(()=>[e(s,{modelValue:t.formData.disk,"onUpdate:modelValue":a[20]||(a[20]=l=>t.formData.disk=l),style:{width:"240px"},clearable:"",placeholder:"请输入硬盘"},null,8,["modelValue"])]),_:1}),e(r,{prop:"network_card",label:"网卡:"},{default:o(()=>[e(s,{modelValue:t.formData.network_card,"onUpdate:modelValue":a[21]||(a[21]=l=>t.formData.network_card=l),style:{width:"240px"},clearable:"",placeholder:"请输入网卡"},null,8,["modelValue"])]),_:1}),e(r,{prop:"operating_system",label:"操作系统:"},{default:o(()=>[e(m,{modelValue:t.formData.operating_system,"onUpdate:modelValue":a[22]||(a[22]=l=>t.formData.operating_system=l),style:{width:"240px"},placeholder:"请选择操作系统"},{default:o(()=>[(i(!0),c(h,null,V(t.operatingsystems,l=>(i(),g(d,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"remarks",label:"备注:"},{default:o(()=>[e(s,{modelValue:t.formData.remarks,"onUpdate:modelValue":a[23]||(a[23]=l=>t.formData.remarks=l),style:{width:"240px"},clearable:"",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1}),e(r,{prop:"weak_password_exists",label:"是否存在弱密码:"},{default:o(()=>[e(m,{modelValue:t.formData.weak_password_exists,"onUpdate:modelValue":a[24]||(a[24]=l=>t.formData.weak_password_exists=l),style:{width:"240px"},placeholder:"请选择是否存在弱密码"},{default:o(()=>[e(d,{label:"是",value:"是"}),e(d,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"weak_password_correction_date",label:"弱密码修正时间:"},{default:o(()=>[e(w,{modelValue:t.formData.weak_password_correction_date,"onUpdate:modelValue":a[25]||(a[25]=l=>t.formData.weak_password_correction_date=l),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"240px"},placeholder:"选择日期"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),e(C,{modelValue:t.dialogVisible.edit,"onUpdate:modelValue":a[55]||(a[55]=l=>t.dialogVisible.edit=l),title:"编辑服务器信息",width:"400","align-center":""},{footer:o(()=>[v("div",le,[e(y,{onClick:a[54]||(a[54]=l=>t.dialogVisible.edit=!1)},{default:o(()=>[b("取消")]),_:1}),e(y,{type:"primary",onClick:_.validateAndSubmitEdit},{default:o(()=>[b("更新")]),_:1},8,["onClick"])])]),default:o(()=>[v("div",ee,[e(P,{model:t.formData,rules:t.rules,ref:"editFormRef","label-position":"right","status-icon":""},{default:o(()=>[e(r,{prop:"management_ip",label:"管理IP:"},{default:o(()=>[e(s,{modelValue:t.formData.management_ip,"onUpdate:modelValue":a[28]||(a[28]=l=>t.formData.management_ip=l),style:{width:"240px"},clearable:"",placeholder:"请输入管理IP"},null,8,["modelValue"])]),_:1}),e(r,{prop:"hostname",label:"主机名:"},{default:o(()=>[e(s,{modelValue:t.formData.hostname,"onUpdate:modelValue":a[29]||(a[29]=l=>t.formData.hostname=l),style:{width:"240px"},clearable:"",placeholder:"请输入主机名"},null,8,["modelValue"])]),_:1}),e(r,{prop:"function_purpose",label:"功能用途:"},{default:o(()=>[e(s,{modelValue:t.formData.function_purpose,"onUpdate:modelValue":a[30]||(a[30]=l=>t.formData.function_purpose=l),style:{width:"240px"},clearable:"",placeholder:"请输入功能用途"},null,8,["modelValue"])]),_:1}),e(r,{prop:"admin1",label:"管理员1:"},{default:o(()=>[e(s,{modelValue:t.formData.admin1,"onUpdate:modelValue":a[31]||(a[31]=l=>t.formData.admin1=l),style:{width:"240px"},clearable:"",placeholder:"请输入管理员1"},null,8,["modelValue"])]),_:1}),e(r,{prop:"admin2",label:"管理员2:"},{default:o(()=>[e(s,{modelValue:t.formData.admin2,"onUpdate:modelValue":a[32]||(a[32]=l=>t.formData.admin2=l),style:{width:"240px"},clearable:"",placeholder:"请输入管理员2"},null,8,["modelValue"])]),_:1}),e(r,{prop:"server_type",label:"服务器类型:"},{default:o(()=>[e(m,{modelValue:t.formData.server_type,"onUpdate:modelValue":a[33]||(a[33]=l=>t.formData.server_type=l),style:{width:"240px"},placeholder:"请选择服务器类型"},{default:o(()=>[(i(!0),c(h,null,V(t.servertypes,l=>(i(),g(d,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"production_attributes",label:"生产属性:"},{default:o(()=>[e(m,{modelValue:t.formData.production_attributes,"onUpdate:modelValue":a[34]||(a[34]=l=>t.formData.production_attributes=l),style:{width:"240px"},placeholder:"请选择生产属性"},{default:o(()=>[(i(!0),c(h,null,V(t.productionattributes,l=>(i(),g(d,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"data_center",label:"所属机房:"},{default:o(()=>[e(m,{modelValue:t.formData.data_center,"onUpdate:modelValue":a[35]||(a[35]=l=>t.formData.data_center=l),style:{width:"240px"},placeholder:"请选择所属机房"},{default:o(()=>[(i(!0),c(h,null,V(t.datacenters,l=>(i(),g(d,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"out_of_band_management_ilo",label:"带外管理IP:"},{default:o(()=>[e(s,{modelValue:t.formData.out_of_band_management_ilo,"onUpdate:modelValue":a[36]||(a[36]=l=>t.formData.out_of_band_management_ilo=l),style:{width:"240px"},clearable:"",placeholder:"请输入带外管理IP"},null,8,["modelValue"])]),_:1}),e(r,{prop:"operation_status",label:"生命周期:"},{default:o(()=>[e(m,{modelValue:t.formData.operation_status,"onUpdate:modelValue":a[37]||(a[37]=l=>t.formData.operation_status=l),style:{width:"240px"},placeholder:"请选择生命周期"},{default:o(()=>[(i(!0),c(h,null,V(t.operationstatus,l=>(i(),g(d,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"is_innovative_tech",label:"是否信创:"},{default:o(()=>[e(m,{modelValue:t.formData.is_innovative_tech,"onUpdate:modelValue":a[38]||(a[38]=l=>t.formData.is_innovative_tech=l),style:{width:"240px"},placeholder:"请选择是否信创"},{default:o(()=>[e(d,{label:"是",value:"是"}),e(d,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"is_single_point",label:"是否单点:"},{default:o(()=>[e(m,{modelValue:t.formData.is_single_point,"onUpdate:modelValue":a[39]||(a[39]=l=>t.formData.is_single_point=l),style:{width:"240px"},placeholder:"请选择是否单点",onChange:_.validateManagedAddresses},{default:o(()=>[e(d,{label:"是",value:"是"}),e(d,{label:"否",value:"否"})]),_:1},8,["modelValue","onChange"])]),_:1}),e(r,{prop:"managed_addresses",label:"互备主机IP:",required:t.formData.is_single_point==="否"},{default:o(()=>[e(s,{modelValue:t.formData.managed_addresses,"onUpdate:modelValue":a[40]||(a[40]=l=>t.formData.managed_addresses=l),style:{width:"240px"},clearable:"",placeholder:"请输入互备主机IP，多个IP用英文逗号分隔"},null,8,["modelValue"])]),_:1},8,["required"]),e(r,{prop:"asset_number",label:"财务资产编号:"},{default:o(()=>[e(s,{modelValue:t.formData.asset_number,"onUpdate:modelValue":a[41]||(a[41]=l=>t.formData.asset_number=l),style:{width:"240px"},clearable:"",placeholder:"请输入财务资产编号"},null,8,["modelValue"])]),_:1}),e(r,{prop:"purchase_date",label:"采购时间:"},{default:o(()=>[e(w,{modelValue:t.formData.purchase_date,"onUpdate:modelValue":a[42]||(a[42]=l=>t.formData.purchase_date=l),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"240px"},placeholder:"选择日期"},null,8,["modelValue"])]),_:1}),e(r,{prop:"maintenance_years",label:"维保年限:"},{default:o(()=>[e(s,{modelValue:t.formData.maintenance_years,"onUpdate:modelValue":a[43]||(a[43]=l=>t.formData.maintenance_years=l),style:{width:"240px"},clearable:"",placeholder:"请输入维保年限"},null,8,["modelValue"])]),_:1}),e(r,{prop:"serial_number",label:"序列号:"},{default:o(()=>[e(s,{modelValue:t.formData.serial_number,"onUpdate:modelValue":a[44]||(a[44]=l=>t.formData.serial_number=l),style:{width:"240px"},clearable:"",placeholder:"请输入序列号"},null,8,["modelValue"])]),_:1}),e(r,{prop:"server_model",label:"服务器型号:"},{default:o(()=>[e(s,{modelValue:t.formData.server_model,"onUpdate:modelValue":a[45]||(a[45]=l=>t.formData.server_model=l),style:{width:"240px"},clearable:"",placeholder:"请输入服务器型号"},null,8,["modelValue"])]),_:1}),e(r,{prop:"cpu_model",label:"CPU型号:"},{default:o(()=>[e(s,{modelValue:t.formData.cpu_model,"onUpdate:modelValue":a[46]||(a[46]=l=>t.formData.cpu_model=l),style:{width:"240px"},clearable:"",placeholder:"请输入CPU型号"},null,8,["modelValue"])]),_:1}),e(r,{prop:"memory",label:"内存:"},{default:o(()=>[e(s,{modelValue:t.formData.memory,"onUpdate:modelValue":a[47]||(a[47]=l=>t.formData.memory=l),style:{width:"240px"},clearable:"",placeholder:"请输入内存"},null,8,["modelValue"])]),_:1}),e(r,{prop:"disk",label:"硬盘:"},{default:o(()=>[e(s,{modelValue:t.formData.disk,"onUpdate:modelValue":a[48]||(a[48]=l=>t.formData.disk=l),style:{width:"240px"},clearable:"",placeholder:"请输入硬盘"},null,8,["modelValue"])]),_:1}),e(r,{prop:"network_card",label:"网卡:"},{default:o(()=>[e(s,{modelValue:t.formData.network_card,"onUpdate:modelValue":a[49]||(a[49]=l=>t.formData.network_card=l),style:{width:"240px"},clearable:"",placeholder:"请输入网卡"},null,8,["modelValue"])]),_:1}),e(r,{prop:"operating_system",label:"操作系统:"},{default:o(()=>[e(m,{modelValue:t.formData.operating_system,"onUpdate:modelValue":a[50]||(a[50]=l=>t.formData.operating_system=l),style:{width:"240px"},placeholder:"请选择操作系统"},{default:o(()=>[(i(!0),c(h,null,V(t.operatingsystems,l=>(i(),g(d,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"remarks",label:"备注:"},{default:o(()=>[e(s,{modelValue:t.formData.remarks,"onUpdate:modelValue":a[51]||(a[51]=l=>t.formData.remarks=l),style:{width:"240px"},clearable:"",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1}),e(r,{prop:"weak_password_exists",label:"是否存在弱密码:"},{default:o(()=>[e(m,{modelValue:t.formData.weak_password_exists,"onUpdate:modelValue":a[52]||(a[52]=l=>t.formData.weak_password_exists=l),style:{width:"240px"},placeholder:"请选择是否存在弱密码"},{default:o(()=>[e(d,{label:"是",value:"是"}),e(d,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"weak_password_correction_date",label:"弱密码修正时间:"},{default:o(()=>[e(w,{modelValue:t.formData.weak_password_correction_date,"onUpdate:modelValue":a[53]||(a[53]=l=>t.formData.weak_password_correction_date=l),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"240px"},placeholder:"选择日期"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),e(C,{modelValue:t.dialogVisible.delete,"onUpdate:modelValue":a[57]||(a[57]=l=>t.dialogVisible.delete=l),title:"删除管理IP",width:"500","align-center":""},{footer:o(()=>[v("div",null,[e(y,{onClick:a[56]||(a[56]=l=>t.dialogVisible.delete=!1)},{default:o(()=>[b("取消")]),_:1}),e(y,{type:"danger",onClick:_.submitDelete},{default:o(()=>[b("确认删除")]),_:1},8,["onClick"])])]),default:o(()=>[e(F,{type:"warning",title:`确定要删除 IP 为 ${t.formData.management_ip} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),e(S,{class:"search-card"},{default:o(()=>[e(P,{inline:!0},{default:o(()=>[e(M,{gutter:10},{default:o(()=>[e(D,{span:6},{default:o(()=>[e(r,{label:"管理IP"},{default:o(()=>[e(s,{modelValue:t.search.management_ip,"onUpdate:modelValue":a[58]||(a[58]=l=>t.search.management_ip=l),placeholder:"请输入管理IP",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(D,{span:6},{default:o(()=>[e(r,{label:"主机名"},{default:o(()=>[e(s,{modelValue:t.search.hostname,"onUpdate:modelValue":a[59]||(a[59]=l=>t.search.hostname=l),placeholder:"请输入主机名",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(D,{span:6},{default:o(()=>[e(r,{label:"服务器类型"},{default:o(()=>[e(m,{modelValue:t.search.server_type,"onUpdate:modelValue":a[60]||(a[60]=l=>t.search.server_type=l),placeholder:"请选择服务器类型",class:"form-control"},{default:o(()=>[e(d,{label:"所有",value:""}),(i(!0),c(h,null,V(t.servertypes,l=>(i(),g(d,{key:l.dict_code,label:l.dict_name,value:l.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(D,{span:6},{default:o(()=>[e(r,{label:"管理员1"},{default:o(()=>[e(s,{modelValue:t.search.admin1,"onUpdate:modelValue":a[61]||(a[61]=l=>t.search.admin1=l),placeholder:"请输入管理员1",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(D,{span:6},{default:o(()=>[e(r,{label:"管理员2"},{default:o(()=>[e(s,{modelValue:t.search.admin2,"onUpdate:modelValue":a[62]||(a[62]=l=>t.search.admin2=l),placeholder:"请输入管理员2",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(D,{span:6},{default:o(()=>[e(r,{label:"生产属性"},{default:o(()=>[e(m,{modelValue:t.search.production_attributes,"onUpdate:modelValue":a[63]||(a[63]=l=>t.search.production_attributes=l),placeholder:"请选择生产属性",class:"form-control"},{default:o(()=>[e(d,{label:"所有",value:""}),(i(!0),c(h,null,V(t.productionattributes,l=>(i(),g(d,{key:l.dict_code,label:l.dict_name,value:l.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(D,{span:6},{default:o(()=>[e(r,{label:"所属机房"},{default:o(()=>[e(m,{modelValue:t.search.data_center,"onUpdate:modelValue":a[64]||(a[64]=l=>t.search.data_center=l),placeholder:"请选择所属机房",class:"form-control"},{default:o(()=>[e(d,{label:"所有",value:""}),(i(!0),c(h,null,V(t.datacenters,l=>(i(),g(d,{key:l.dict_code,label:l.dict_name,value:l.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(D,{span:6},{default:o(()=>[e(r,{label:"生命周期"},{default:o(()=>[e(m,{modelValue:t.search.operation_status,"onUpdate:modelValue":a[65]||(a[65]=l=>t.search.operation_status=l),placeholder:"请选择生命周期",class:"form-control"},{default:o(()=>[e(d,{label:"所有",value:""}),(i(!0),c(h,null,V(t.operationstatus,l=>(i(),g(d,{key:l.dict_code,label:l.dict_name,value:l.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(D,{span:6},{default:o(()=>[e(r,{label:"是否信创"},{default:o(()=>[e(m,{modelValue:t.search.is_innovative_tech,"onUpdate:modelValue":a[66]||(a[66]=l=>t.search.is_innovative_tech=l),placeholder:"请选择是否信创",class:"form-control"},{default:o(()=>[e(d,{label:"所有",value:""}),e(d,{label:"是",value:"是"}),e(d,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(D,{span:6},{default:o(()=>[e(r,{label:"是否监控"},{default:o(()=>[e(m,{modelValue:t.search.is_monitored,"onUpdate:modelValue":a[67]||(a[67]=l=>t.search.is_monitored=l),placeholder:"请选择是否监控",class:"form-control"},{default:o(()=>[e(d,{label:"所有",value:""}),e(d,{label:"是",value:"是"}),e(d,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(D,{span:6},{default:o(()=>[e(r,{label:"是否在线"},{default:o(()=>[e(m,{modelValue:t.search.online_status,"onUpdate:modelValue":a[68]||(a[68]=l=>t.search.online_status=l),placeholder:"请选择是否在线",class:"form-control"},{default:o(()=>[e(d,{label:"所有",value:""}),e(d,{label:"在线",value:"在线"}),e(d,{label:"离线",value:"离线"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(D,{span:6},{default:o(()=>[e(r,{label:"带外管理IP"},{default:o(()=>[e(s,{modelValue:t.search.out_of_band_management_ilo,"onUpdate:modelValue":a[69]||(a[69]=l=>t.search.out_of_band_management_ilo=l),placeholder:"请输入带外管理IP",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(D,{span:24,class:"search-buttons-col"},{default:o(()=>[e(r,{label:" ",class:"form-item-with-label search-buttons"},{default:o(()=>[v("div",ae,[e(y,{type:"primary",onClick:_.loadData},{default:o(()=>[e(I,null,{default:o(()=>[e(A)]),_:1}),b("查询 ")]),_:1},8,["onClick"]),e(y,{onClick:_.resetSearch},{default:o(()=>[b("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),v("div",te,[v("div",oe,[e(y,{type:"success",disabled:!t.hasInsertPermission,onClick:_.handleAdd},{default:o(()=>[e(I,null,{default:o(()=>[e(q)]),_:1}),b("新增资产 ")]),_:1},8,["disabled","onClick"])]),v("div",re,[e(y,{type:"info",onClick:_.exportData},{default:o(()=>[e(I,null,{default:o(()=>[e(z)]),_:1}),b(" 导出数据 ")]),_:1},8,["onClick"])])]),e(S,{class:"table-card"},{default:o(()=>[K((i(),g(R,{data:t.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:_.handleSortChange},{default:o(()=>[G("",!0),e(n,{prop:"management_ip",label:"管理IP",sortable:""}),e(n,{prop:"hostname",label:"主机名",sortable:""}),e(n,{prop:"function_purpose",label:"功能用途",sortable:""}),e(n,{prop:"admin1",label:"管理员1",sortable:""}),e(n,{prop:"admin2",label:"管理员2",sortable:""}),e(n,{prop:"server_type",label:"服务器类型",sortable:""}),e(n,{prop:"production_attributes",label:"生产属性",sortable:""}),e(n,{prop:"data_center",label:"所属机房",sortable:""}),e(n,{prop:"out_of_band_management_ilo",label:"带外管理IP",sortable:""}),e(n,{prop:"operation_status",label:"生命周期",sortable:""},{default:o(l=>[e(k,{type:l.row.operation_status==="正常"?"success":"danger"},{default:o(()=>[b(x(l.row.operation_status),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"is_innovative_tech",label:"是否信创",sortable:""},{default:o(l=>[e(k,{type:l.row.is_innovative_tech==="是"?"success":"info"},{default:o(()=>[b(x(l.row.is_innovative_tech),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"asset_number",label:"财务资产编号",sortable:""}),e(n,{prop:"purchase_date",label:"采购时间",sortable:""}),e(n,{prop:"maintenance_years",label:"维保年限",sortable:""}),e(n,{prop:"maintenance_end_date",label:"维保截止日期",sortable:""}),e(n,{prop:"serial_number",label:"序列号",sortable:""}),e(n,{prop:"server_model",label:"服务器型号",sortable:""}),e(n,{prop:"is_monitored",label:"是否监控",sortable:""},{default:o(l=>[e(k,{type:l.row.is_monitored==="是"?"success":"info"},{default:o(()=>[b(x(l.row.is_monitored),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"online_status",label:"是否在线",sortable:""},{default:o(l=>[e(k,{type:l.row.online_status==="在线"?"success":"danger"},{default:o(()=>[b(x(l.row.online_status),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"is_single_point",label:"是否单点",sortable:""},{default:o(l=>[e(k,{type:l.row.is_single_point==="是"?"info":"warning"},{default:o(()=>[b(x(l.row.is_single_point),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"managed_addresses",label:"互备主机IP",sortable:""}),e(n,{prop:"cpu_model",label:"CPU型号",sortable:""}),e(n,{prop:"memory",label:"内存",sortable:""}),e(n,{prop:"disk",label:"硬盘",sortable:""}),e(n,{prop:"network_card",label:"网卡",sortable:""}),e(n,{prop:"operating_system",label:"操作系统",sortable:""}),e(n,{prop:"remarks",label:"备注",sortable:""}),e(n,{prop:"weak_password_exists",label:"是否存在弱密码",sortable:""},{default:o(l=>[e(k,{type:l.row.weak_password_exists==="是"?"warning":"success"},{default:o(()=>[b(x(l.row.weak_password_exists),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"weak_password_correction_date",label:"弱密码修正时间",sortable:""}),e(n,{prop:"created_at",label:"创建时间",sortable:""}),e(n,{prop:"created_by",label:"创建人",sortable:""}),e(n,{prop:"updated_at",label:"更新时间",sortable:""}),e(n,{prop:"updated_by",label:"更新人",sortable:""}),e(n,{label:"操作",fixed:"right"},{default:o(l=>[v("div",se,[e(y,{size:"small",type:"warning",disabled:!t.hasUpdatePermission,onClick:T=>_.handleEdit(l.$index,l.row)},{default:o(()=>[b("编辑")]),_:2},1032,["disabled","onClick"]),e(y,{size:"small",type:"danger",disabled:!t.hasDeletePermission,onClick:T=>_.handleDelete(l.$index,l.row)},{default:o(()=>[b("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[B,t.loading]]),v("div",de,[e(E,{background:"","current-page":t.search.currentPage,"page-size":t.search.pageSize,total:t.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:_.handlePageSizeChange,onCurrentChange:_.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const me=N(W,[["render",ne],["__scopeId","data-v-1f98ea36"]]);export{me as default};
