import{_ as O,z as R,v as T,A as q,c as D,a as t,f as s,w as o,b as m,F as w,l as C,h as _,B as G,C as H,m as x,x as J,p as K,q as M,e as b}from"./index-C0WPPkX4.js";import{u as k,w as Q,F as W}from"./FileSaver.min-BAET_C7X.js";const X={components:{Plus:q,Search:T,Download:R},data(){var i,e,c;return{userArr:[],loading:!1,productionattributes:[],hasDeletePermission:(i=localStorage.getItem("role_code"))==null?void 0:i.includes("D"),hasUpdatePermission:(e=localStorage.getItem("role_code"))==null?void 0:e.includes("U"),hasInsertPermission:(c=localStorage.getItem("role_code"))==null?void 0:c.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{system_abbreviation:"",main_admin:"",production_attribute:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,external_system_id:"",system_abbreviation:"",main_admin:"",backup_admin:"",production_attribute:"",system_provider:"",system_function_summary:"",business_department:"",system_form:"",cs_client_name:"",bs_url:"",ip_port:"",monitoring_system_name:"",major_milestones:""}}},mounted(){this.loadData(),this.getDatadict("C","productionattributes")},methods:{handlePageChange(i){this.search.currentPage=i,this.loadData()},handlePageSizeChange(i){this.search.pageSize=parseInt(i),this.search.currentPage=1,this.loadData()},handleSortChange({prop:i,order:e}){this.search.sortProp=i,this.search.sortOrder=e==="ascending"?"asc":"desc",this.loadData()},resetSearch(){this.search={system_abbreviation:"",main_admin:"",production_attribute:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},async loadData(){try{this.loading=!0;const i=await this.$axios.post("/api/get_cmdb_system_admin_responsibility",this.search);this.userArr=i.data.msg,this.search.total=i.data.total}catch(i){console.error("数据加载失败:",i),this.$message.error("数据加载失败")}finally{this.loading=!1}},async submitAdd(){try{await this.$axios.post("/api/add_cmdb_system_admin_responsibility",this.formData),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(i){console.error("添加失败:",i),this.$message.error("添加失败")}},async submitEdit(){try{await this.$axios.post("/api/update_cmdb_system_admin_responsibility",this.formData),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(i){console.error("更新失败:",i),this.$message.error("更新失败")}},async submitDelete(){try{await this.$axios.post("/api/del_cmdb_system_admin_responsibility",this.formData),this.$message.success("删除成功"),this.loadData(),this.dialogVisible.delete=!1}catch(i){console.error("删除失败:",i),this.$message.error("删除失败")}},async getDatadict(i,e){try{const c=await this.$axios.post("/api/get_cmdb_data_dictionary",{dict_code:i});this[e]=c.data.msg}catch(c){console.error("数据加载失败:",c),this.$message.error("数据加载失败")}},handleAdd(){this.dialogVisible.add=!this.dialogVisible.add,this.formData={}},handleEdit(i,e){this.dialogVisible.edit=!0,this.formData.id=e.id,this.formData.external_system_id=e.external_system_id,this.formData.system_abbreviation=e.system_abbreviation,this.formData.main_admin=e.main_admin,this.formData.backup_admin=e.backup_admin,this.formData.production_attribute=e.production_attribute,this.formData.system_provider=e.system_provider,this.formData.system_function_summary=e.system_function_summary,this.formData.business_department=e.business_department,this.formData.system_form=e.system_form,this.formData.cs_client_name=e.cs_client_name,this.formData.bs_url=e.bs_url,this.formData.ip_port=e.ip_port,this.formData.monitoring_system_name=e.monitoring_system_name,this.formData.major_milestones=e.major_milestones},handleDelete(i,e){this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=e.id,this.formData.external_system_id=e.external_system_id},exportData(){const e=this.$refs.table.columns,c=e.map(u=>u.label),U=this.userArr.map(u=>e.map(h=>u[h.property])),a=[c,...U],p=k.aoa_to_sheet(a),n=k.book_new();k.book_append_sheet(n,p,"Sheet1");const f=Q(n,{bookType:"xlsx",type:"array"}),y=new Blob([f],{type:"application/octet-stream"});W.saveAs(y,"系统管理员责任表-外部机构.xlsx")}}},r=i=>(K("data-v-41a88fce"),i=i(),M(),i),Y={class:"user-manage"},Z={class:"dialogdiv"},$=r(()=>s("span",{class:"label"},"外部系统编号:",-1)),ee=r(()=>s("span",{class:"label"},"业务系统简称:",-1)),le=r(()=>s("span",{class:"label"},"主岗:",-1)),te=r(()=>s("span",{class:"label"},"备岗:",-1)),ae=r(()=>s("span",{class:"label"},"生产属性:",-1)),se=r(()=>s("span",{class:"label"},"系统提供方:",-1)),oe=r(()=>s("span",{class:"label"},"系统功能简述:",-1)),ne=r(()=>s("span",{class:"label"},"负责该系统的业务部门:",-1)),ie=r(()=>s("span",{class:"label"},"系统形态:",-1)),re=r(()=>s("span",{class:"label"},"CS客户端程序名称:",-1)),de=r(()=>s("span",{class:"label"},"BS URL地址:",-1)),me=r(()=>s("span",{class:"label"},"IP:端口:",-1)),ue=r(()=>s("span",{class:"label"},"监控调用的业务系统名称:",-1)),pe=r(()=>s("span",{class:"label"},"重大历程:",-1)),_e={class:"dialog-footer"},ce={class:"dialogdiv"},be=r(()=>s("span",{class:"label"},"外部系统编号:",-1)),fe=r(()=>s("span",{class:"label"},"业务系统简称:",-1)),ye=r(()=>s("span",{class:"label"},"主岗:",-1)),he=r(()=>s("span",{class:"label"},"备岗:",-1)),Ve=r(()=>s("span",{class:"label"},"生产属性:",-1)),ge=r(()=>s("span",{class:"label"},"系统提供方:",-1)),De=r(()=>s("span",{class:"label"},"系统功能简述:",-1)),xe=r(()=>s("span",{class:"label"},"负责该系统的业务部门:",-1)),ve=r(()=>s("span",{class:"label"},"系统形态:",-1)),we=r(()=>s("span",{class:"label"},"CS客户端程序名称:",-1)),Ce=r(()=>s("span",{class:"label"},"BS URL地址:",-1)),ke=r(()=>s("span",{class:"label"},"IP:端口:",-1)),Ue=r(()=>s("span",{class:"label"},"监控调用的业务系统名称:",-1)),Se=r(()=>s("span",{class:"label"},"重大历程:",-1)),Pe={class:"dialog-footer"},ze={class:"button-container"},Ie={class:"action-bar unified-action-bar"},Be={class:"action-bar-left"},Ae={class:"action-bar-right"},je={style:{display:"flex","white-space":"nowrap"}},Ee={class:"pagination"};function Fe(i,e,c,U,a,p){const n=m("el-input"),f=m("el-option"),y=m("el-select"),u=m("el-button"),h=m("el-dialog"),P=m("el-alert"),V=m("el-form-item"),g=m("el-col"),z=m("Search"),v=m("el-icon"),I=m("el-row"),B=m("el-form"),S=m("el-card"),A=m("Plus"),j=m("Download"),d=m("el-table-column"),E=m("el-table"),F=m("el-pagination"),L=H("loading");return b(),D("div",Y,[t(h,{modelValue:a.dialogVisible.add,"onUpdate:modelValue":e[15]||(e[15]=l=>a.dialogVisible.add=l),title:"新增系统信息",width:"400","align-center":""},{footer:o(()=>[s("div",_e,[t(u,{onClick:e[14]||(e[14]=l=>a.dialogVisible.add=!1)},{default:o(()=>[_("返回")]),_:1}),t(u,{type:"primary",onClick:p.submitAdd},{default:o(()=>[_("确定")]),_:1},8,["onClick"])])]),default:o(()=>[s("div",Z,[s("p",null,[$,t(n,{modelValue:a.formData.external_system_id,"onUpdate:modelValue":e[0]||(e[0]=l=>a.formData.external_system_id=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[ee,t(n,{modelValue:a.formData.system_abbreviation,"onUpdate:modelValue":e[1]||(e[1]=l=>a.formData.system_abbreviation=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[le,t(n,{modelValue:a.formData.main_admin,"onUpdate:modelValue":e[2]||(e[2]=l=>a.formData.main_admin=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[te,t(n,{modelValue:a.formData.backup_admin,"onUpdate:modelValue":e[3]||(e[3]=l=>a.formData.backup_admin=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[ae,t(y,{modelValue:a.formData.production_attribute,"onUpdate:modelValue":e[4]||(e[4]=l=>a.formData.production_attribute=l),style:{width:"240px"},placeholder:"请选择生产属性"},{default:o(()=>[(b(!0),D(w,null,C(a.productionattributes,l=>(b(),x(f,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[se,t(n,{modelValue:a.formData.system_provider,"onUpdate:modelValue":e[5]||(e[5]=l=>a.formData.system_provider=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[oe,t(n,{type:"textarea",rows:2,modelValue:a.formData.system_function_summary,"onUpdate:modelValue":e[6]||(e[6]=l=>a.formData.system_function_summary=l),style:{width:"240px"}},null,8,["modelValue"])]),s("p",null,[ne,t(n,{modelValue:a.formData.business_department,"onUpdate:modelValue":e[7]||(e[7]=l=>a.formData.business_department=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[ie,t(n,{modelValue:a.formData.system_form,"onUpdate:modelValue":e[8]||(e[8]=l=>a.formData.system_form=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[re,t(n,{modelValue:a.formData.cs_client_name,"onUpdate:modelValue":e[9]||(e[9]=l=>a.formData.cs_client_name=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[de,t(n,{modelValue:a.formData.bs_url,"onUpdate:modelValue":e[10]||(e[10]=l=>a.formData.bs_url=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[me,t(n,{modelValue:a.formData.ip_port,"onUpdate:modelValue":e[11]||(e[11]=l=>a.formData.ip_port=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[ue,t(n,{modelValue:a.formData.monitoring_system_name,"onUpdate:modelValue":e[12]||(e[12]=l=>a.formData.monitoring_system_name=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[pe,t(n,{type:"textarea",rows:2,modelValue:a.formData.major_milestones,"onUpdate:modelValue":e[13]||(e[13]=l=>a.formData.major_milestones=l),style:{width:"240px"}},null,8,["modelValue"])])])]),_:1},8,["modelValue"]),t(h,{modelValue:a.dialogVisible.edit,"onUpdate:modelValue":e[31]||(e[31]=l=>a.dialogVisible.edit=l),title:"编辑系统信息",width:"400","align-center":""},{footer:o(()=>[s("div",Pe,[t(u,{onClick:e[30]||(e[30]=l=>a.dialogVisible.edit=!1)},{default:o(()=>[_("取消")]),_:1}),t(u,{type:"primary",onClick:p.submitEdit},{default:o(()=>[_("更新")]),_:1},8,["onClick"])])]),default:o(()=>[s("div",ce,[s("p",null,[be,t(n,{modelValue:a.formData.external_system_id,"onUpdate:modelValue":e[16]||(e[16]=l=>a.formData.external_system_id=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[fe,t(n,{modelValue:a.formData.system_abbreviation,"onUpdate:modelValue":e[17]||(e[17]=l=>a.formData.system_abbreviation=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[ye,t(n,{modelValue:a.formData.main_admin,"onUpdate:modelValue":e[18]||(e[18]=l=>a.formData.main_admin=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[he,t(n,{modelValue:a.formData.backup_admin,"onUpdate:modelValue":e[19]||(e[19]=l=>a.formData.backup_admin=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[Ve,t(y,{modelValue:a.formData.production_attribute,"onUpdate:modelValue":e[20]||(e[20]=l=>a.formData.production_attribute=l),style:{width:"240px"},placeholder:"请选择生产属性"},{default:o(()=>[(b(!0),D(w,null,C(a.productionattributes,l=>(b(),x(f,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[ge,t(n,{modelValue:a.formData.system_provider,"onUpdate:modelValue":e[21]||(e[21]=l=>a.formData.system_provider=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[De,t(n,{type:"textarea",rows:2,modelValue:a.formData.system_function_summary,"onUpdate:modelValue":e[22]||(e[22]=l=>a.formData.system_function_summary=l),style:{width:"240px"}},null,8,["modelValue"])]),s("p",null,[xe,t(n,{modelValue:a.formData.business_department,"onUpdate:modelValue":e[23]||(e[23]=l=>a.formData.business_department=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[ve,t(n,{modelValue:a.formData.system_form,"onUpdate:modelValue":e[24]||(e[24]=l=>a.formData.system_form=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[we,t(n,{modelValue:a.formData.cs_client_name,"onUpdate:modelValue":e[25]||(e[25]=l=>a.formData.cs_client_name=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[Ce,t(n,{modelValue:a.formData.bs_url,"onUpdate:modelValue":e[26]||(e[26]=l=>a.formData.bs_url=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[ke,t(n,{modelValue:a.formData.ip_port,"onUpdate:modelValue":e[27]||(e[27]=l=>a.formData.ip_port=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[Ue,t(n,{modelValue:a.formData.monitoring_system_name,"onUpdate:modelValue":e[28]||(e[28]=l=>a.formData.monitoring_system_name=l),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[Se,t(n,{type:"textarea",rows:2,modelValue:a.formData.major_milestones,"onUpdate:modelValue":e[29]||(e[29]=l=>a.formData.major_milestones=l),style:{width:"240px"}},null,8,["modelValue"])])])]),_:1},8,["modelValue"]),t(h,{modelValue:a.dialogVisible.delete,"onUpdate:modelValue":e[33]||(e[33]=l=>a.dialogVisible.delete=l),title:"删除外部系统编号",width:"500","align-center":""},{footer:o(()=>[s("div",null,[t(u,{onClick:e[32]||(e[32]=l=>a.dialogVisible.delete=!1)},{default:o(()=>[_("取消")]),_:1}),t(u,{type:"danger",onClick:p.submitDelete},{default:o(()=>[_("确认删除")]),_:1},8,["onClick"])])]),default:o(()=>[t(P,{type:"warning",title:`确定要删除 外部系统编号 为 ${a.formData.external_system_id} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),t(S,{class:"search-card"},{default:o(()=>[t(B,{inline:!0},{default:o(()=>[t(I,{gutter:10},{default:o(()=>[t(g,{span:6},{default:o(()=>[t(V,{label:"业务系统简称"},{default:o(()=>[t(n,{modelValue:a.search.system_abbreviation,"onUpdate:modelValue":e[34]||(e[34]=l=>a.search.system_abbreviation=l),placeholder:"请输入业务系统简称",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),t(g,{span:6},{default:o(()=>[t(V,{label:"主岗"},{default:o(()=>[t(n,{modelValue:a.search.main_admin,"onUpdate:modelValue":e[35]||(e[35]=l=>a.search.main_admin=l),placeholder:"请输入主岗",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),t(g,{span:6},{default:o(()=>[t(V,{label:"生产属性"},{default:o(()=>[t(y,{modelValue:a.search.production_attribute,"onUpdate:modelValue":e[36]||(e[36]=l=>a.search.production_attribute=l),placeholder:"请选择生产属性",class:"form-control"},{default:o(()=>[t(f,{label:"所有",value:""}),(b(!0),D(w,null,C(a.productionattributes,l=>(b(),x(f,{key:l.dict_code,label:l.dict_name,value:l.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(g,{span:6,class:"search-buttons-col"},{default:o(()=>[t(V,{label:" ",class:"form-item-with-label search-buttons"},{default:o(()=>[s("div",ze,[t(u,{type:"primary",onClick:p.loadData},{default:o(()=>[t(v,null,{default:o(()=>[t(z)]),_:1}),_("查询 ")]),_:1},8,["onClick"]),t(u,{onClick:p.resetSearch},{default:o(()=>[_("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),s("div",Ie,[s("div",Be,[t(u,{type:"success",disabled:!a.hasInsertPermission,onClick:p.handleAdd},{default:o(()=>[t(v,null,{default:o(()=>[t(A)]),_:1}),_("新增系统 ")]),_:1},8,["disabled","onClick"])]),s("div",Ae,[t(u,{type:"info",onClick:p.exportData},{default:o(()=>[t(v,null,{default:o(()=>[t(j)]),_:1}),_(" 导出数据 ")]),_:1},8,["onClick"])])]),t(S,{class:"table-card"},{default:o(()=>[G((b(),x(E,{data:a.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:p.handleSortChange},{default:o(()=>[J("",!0),t(d,{prop:"external_system_id",label:"外部系统编号",sortable:""}),t(d,{prop:"system_abbreviation",label:"业务系统简称",sortable:""}),t(d,{prop:"main_admin",label:"主岗",sortable:""}),t(d,{prop:"backup_admin",label:"备岗",sortable:""}),t(d,{prop:"production_attribute",label:"生产属性",sortable:""}),t(d,{prop:"system_provider",label:"系统提供方",sortable:""}),t(d,{prop:"system_function_summary",label:"系统功能简述",sortable:""}),t(d,{prop:"business_department",label:"负责该系统的业务部门",sortable:""}),t(d,{prop:"system_form",label:"系统形态",sortable:""}),t(d,{prop:"cs_client_name",label:"CS客户端程序名称",sortable:""}),t(d,{prop:"bs_url",label:"BS URL地址",sortable:""}),t(d,{prop:"ip_port",label:"IP:端口",sortable:""}),t(d,{prop:"monitoring_system_name",label:"业务系统英文简称",sortable:""}),t(d,{prop:"major_milestones",label:"重大事件",sortable:""}),t(d,{prop:"created_at",label:"创建时间",sortable:""}),t(d,{prop:"created_by",label:"创建人",sortable:""}),t(d,{prop:"updated_at",label:"更新时间",sortable:""}),t(d,{prop:"updated_by",label:"更新人",sortable:""}),t(d,{label:"操作",fixed:"right"},{default:o(l=>[s("div",je,[t(u,{size:"small",type:"warning",disabled:!a.hasUpdatePermission,onClick:N=>p.handleEdit(l.$index,l.row)},{default:o(()=>[_("编辑")]),_:2},1032,["disabled","onClick"]),t(u,{size:"small",type:"danger",disabled:!a.hasDeletePermission,onClick:N=>p.handleDelete(l.$index,l.row)},{default:o(()=>[_("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[L,a.loading]]),s("div",Ee,[t(F,{background:"","current-page":a.search.currentPage,"page-size":a.search.pageSize,total:a.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:p.handlePageSizeChange,onCurrentChange:p.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const Oe=O(X,[["render",Fe],["__scopeId","data-v-41a88fce"]]);export{Oe as default};
