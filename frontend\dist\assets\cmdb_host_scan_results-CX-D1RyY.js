import{_ as N,z as O,v as T,A as j,c as D,a as e,f as d,w as l,b as n,F as x,l as I,h as u,t as k,B as G,C as H,m as w,p as J,q as K,e as b}from"./index-C0WPPkX4.js";import{u as C,w as M,F as Q}from"./FileSaver.min-BAET_C7X.js";const W={components:{Plus:j,Search:T,Download:O},data(){var o,a,r;return{userArr:[],loading:!1,hasDeletePermission:(o=localStorage.getItem("role_code"))==null?void 0:o.includes("D"),hasUpdatePermission:(a=localStorage.getItem("role_code"))==null?void 0:a.includes("U"),hasInsertPermission:(r=localStorage.getItem("role_code"))==null?void 0:r.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{management_ip:"",function_purpose:"",admin1:"",admin2:"",designated_admin:"",management_status:"",is_virtual_machine:"",online_status:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,management_ip:"",designated_admin:"",remarks:""},userList:[],rules:{management_ip:[{required:!0,message:"请输入IP地址",trigger:"blur"},{pattern:/^(\d{1,3}\.){3}\d{1,3}$/,message:"请输入有效的IP地址",trigger:"blur"}],designated_admin:[{max:100,message:"指定管理员长度不能超过100个字符",trigger:"blur"}],remarks:[{max:500,message:"备注长度不能超过500个字符",trigger:"blur"}]}}},mounted(){this.$route.query.search_ip&&(this.search.management_ip=this.$route.query.search_ip),this.loadData(),this.loadUserList()},methods:{handlePageChange(o){this.search.currentPage=o,this.loadData()},handlePageSizeChange(o){this.search.pageSize=parseInt(o),this.search.currentPage=1,this.loadData()},handleSortChange({prop:o,order:a}){this.search.sortProp=o,this.search.sortOrder=a==="ascending"?"asc":"desc",this.loadData()},resetSearch(){this.search={management_ip:"",function_purpose:"",admin1:"",admin2:"",designated_admin:"",management_status:"",is_virtual_machine:"",online_status:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},async loadData(){try{this.loading=!0;const o=await this.$axios.post("/api/get_cmdb_host_scan_results",this.search);this.userArr=o.data.msg,this.search.total=o.data.total}catch(o){console.error("数据加载失败:",o),this.$message.error("数据加载失败")}finally{this.loading=!1}},exportData(){const a=this.$refs.table.columns,r=a.map(h=>h.label),P=this.userArr.map(h=>a.map(V=>h[V.property])),s=[r,...P],c=C.aoa_to_sheet(s),_=C.book_new();C.book_append_sheet(_,c,"Sheet1");const i=M(_,{bookType:"xlsx",type:"array"}),p=new Blob([i],{type:"application/octet-stream"});Q.saveAs(p,"IP资产管理.xlsx")},handleEdit(o,a){this.dialogVisible.edit=!0,this.formData.id=a.id,this.formData.management_ip=a.management_ip,this.formData.designated_admin=a.designated_admin,this.formData.remarks=a.remarks,this.userList.length===0&&this.loadUserList()},async validateAndSubmitEdit(){try{await this.$refs.editFormRef.validate(),await this.submitEdit()}catch{this.$message.error("请完善必填项后再提交")}},async submitEdit(){var o,a;try{const r={...this.formData,usernameby:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/update_cmdb_host_scan_results",r),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(r){console.error("更新失败:",r),this.$message.error(((a=(o=r.response)==null?void 0:o.data)==null?void 0:a.msg)||"更新失败")}},async loadUserList(){try{const o=await this.$axios.post("/api/get_all_users_real_name");o.data.code===0?this.userList=o.data.msg||[]:(console.error("获取用户列表失败:",o.data.msg),this.$message.error("获取用户列表失败"))}catch(o){console.error("获取用户列表失败:",o),this.$message.error("获取用户列表失败")}},handleAdd(){this.dialogVisible.add=!0,this.formData={management_ip:"",designated_admin:"",remarks:""},this.userList.length===0&&this.loadUserList(),this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields()})},async validateAndSubmitAdd(){try{await this.$refs.addFormRef.validate(),await this.submitAdd()}catch{this.$message.error("请完善必填项后再提交")}},async submitAdd(){var o,a;try{const r={...this.formData,usernameby:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/add_cmdb_host_scan_results",r),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(r){console.error("添加失败:",r),this.$message.error(((a=(o=r.response)==null?void 0:o.data)==null?void 0:a.msg)||"添加失败")}},handleDelete(o,a){this.dialogVisible.delete=!0,this.formData.id=a.id,this.formData.management_ip=a.management_ip},async submitDelete(){var o,a;try{const r={id:this.formData.id,usernameby:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/delete_cmdb_host_scan_results",r),this.$message.success("删除成功"),this.dialogVisible.delete=!1,this.loadData()}catch(r){console.error("删除失败:",r),this.$message.error(((a=(o=r.response)==null?void 0:o.data)==null?void 0:a.msg)||"删除失败")}}}},X=o=>(J("data-v-4f6aeea1"),o=o(),K(),o),Y={class:"user-manage"},Z={class:"dialogdiv"},$={class:"dialog-footer"},ee={class:"dialogdiv"},ae=X(()=>d("p",null,"此操作不可恢复！",-1)),le={class:"dialog-footer"},te={class:"dialogdiv"},se={class:"dialog-footer"},oe={class:"button-container"},re={class:"action-bar unified-action-bar"},ne={class:"action-bar-left"},ie={class:"action-bar-right"},de={style:{display:"flex","white-space":"nowrap"}},me={class:"pagination"};function ue(o,a,r,P,s,c){const _=n("el-input"),i=n("el-form-item"),p=n("el-option"),h=n("el-select"),V=n("el-form"),g=n("el-button"),v=n("el-dialog"),f=n("el-col"),U=n("Search"),y=n("el-icon"),A=n("el-row"),S=n("el-card"),z=n("Plus"),F=n("Download"),m=n("el-table-column"),L=n("el-tag"),E=n("el-table"),q=n("el-pagination"),B=H("loading");return b(),D("div",Y,[e(v,{modelValue:s.dialogVisible.add,"onUpdate:modelValue":a[4]||(a[4]=t=>s.dialogVisible.add=t),title:"新增IP资产",width:"400","align-center":""},{footer:l(()=>[d("div",$,[e(g,{onClick:a[3]||(a[3]=t=>s.dialogVisible.add=!1)},{default:l(()=>[u("返回")]),_:1}),e(g,{type:"primary",onClick:c.validateAndSubmitAdd},{default:l(()=>[u("确定")]),_:1},8,["onClick"])])]),default:l(()=>[d("div",Z,[e(V,{model:s.formData,rules:s.rules,ref:"addFormRef","label-position":"right","status-icon":""},{default:l(()=>[e(i,{prop:"management_ip",label:"IP地址:"},{default:l(()=>[e(_,{modelValue:s.formData.management_ip,"onUpdate:modelValue":a[0]||(a[0]=t=>s.formData.management_ip=t),style:{width:"240px"},clearable:"",placeholder:"请输入IP地址"},null,8,["modelValue"])]),_:1}),e(i,{prop:"designated_admin",label:"指定管理员:"},{default:l(()=>[e(h,{modelValue:s.formData.designated_admin,"onUpdate:modelValue":a[1]||(a[1]=t=>s.formData.designated_admin=t),style:{width:"240px"},filterable:"",clearable:"",placeholder:"请选择指定管理员"},{default:l(()=>[(b(!0),D(x,null,I(s.userList,t=>(b(),w(p,{key:t.id,label:t.real_name||t.username,value:t.real_name||t.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{prop:"remarks",label:"备注:"},{default:l(()=>[e(_,{modelValue:s.formData.remarks,"onUpdate:modelValue":a[2]||(a[2]=t=>s.formData.remarks=t),style:{width:"240px"},type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),e(v,{modelValue:s.dialogVisible.delete,"onUpdate:modelValue":a[6]||(a[6]=t=>s.dialogVisible.delete=t),title:"确认删除",width:"400","align-center":""},{footer:l(()=>[d("div",le,[e(g,{onClick:a[5]||(a[5]=t=>s.dialogVisible.delete=!1)},{default:l(()=>[u("取消")]),_:1}),e(g,{type:"danger",onClick:c.submitDelete},{default:l(()=>[u("确定删除")]),_:1},8,["onClick"])])]),default:l(()=>[d("div",ee,[d("p",null,[u("确定要删除IP地址为 "),d("strong",null,k(s.formData.management_ip),1),u(" 的记录吗？")]),ae])]),_:1},8,["modelValue"]),e(v,{modelValue:s.dialogVisible.edit,"onUpdate:modelValue":a[11]||(a[11]=t=>s.dialogVisible.edit=t),title:"编辑IP资产管理",width:"400","align-center":""},{footer:l(()=>[d("div",se,[e(g,{onClick:a[10]||(a[10]=t=>s.dialogVisible.edit=!1)},{default:l(()=>[u("返回")]),_:1}),e(g,{type:"primary",onClick:c.validateAndSubmitEdit},{default:l(()=>[u("确定")]),_:1},8,["onClick"])])]),default:l(()=>[d("div",te,[e(V,{model:s.formData,rules:s.rules,ref:"editFormRef","label-position":"right","status-icon":""},{default:l(()=>[e(i,{prop:"management_ip",label:"IP地址:"},{default:l(()=>[e(_,{modelValue:s.formData.management_ip,"onUpdate:modelValue":a[7]||(a[7]=t=>s.formData.management_ip=t),style:{width:"240px"},clearable:"",placeholder:"请输入IP地址",disabled:""},null,8,["modelValue"])]),_:1}),e(i,{prop:"designated_admin",label:"指定管理员:"},{default:l(()=>[e(h,{modelValue:s.formData.designated_admin,"onUpdate:modelValue":a[8]||(a[8]=t=>s.formData.designated_admin=t),style:{width:"240px"},filterable:"",clearable:"",placeholder:"请选择指定管理员"},{default:l(()=>[(b(!0),D(x,null,I(s.userList,t=>(b(),w(p,{key:t.id,label:t.real_name||t.username,value:t.real_name||t.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{prop:"remarks",label:"备注:"},{default:l(()=>[e(_,{modelValue:s.formData.remarks,"onUpdate:modelValue":a[9]||(a[9]=t=>s.formData.remarks=t),style:{width:"240px"},type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),e(S,{class:"search-card"},{default:l(()=>[e(V,{inline:!0},{default:l(()=>[e(A,{gutter:10},{default:l(()=>[e(f,{span:6},{default:l(()=>[e(i,{label:"IP地址"},{default:l(()=>[e(_,{modelValue:s.search.management_ip,"onUpdate:modelValue":a[12]||(a[12]=t=>s.search.management_ip=t),placeholder:"请输入IP地址",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(f,{span:6},{default:l(()=>[e(i,{label:"功能用途"},{default:l(()=>[e(_,{modelValue:s.search.function_purpose,"onUpdate:modelValue":a[13]||(a[13]=t=>s.search.function_purpose=t),placeholder:"请输入功能用途",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(f,{span:6},{default:l(()=>[e(i,{label:"管理员1"},{default:l(()=>[e(_,{modelValue:s.search.admin1,"onUpdate:modelValue":a[14]||(a[14]=t=>s.search.admin1=t),placeholder:"请输入管理员1",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(f,{span:6},{default:l(()=>[e(i,{label:"管理员2"},{default:l(()=>[e(_,{modelValue:s.search.admin2,"onUpdate:modelValue":a[15]||(a[15]=t=>s.search.admin2=t),placeholder:"请输入管理员2",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(f,{span:6},{default:l(()=>[e(i,{label:"指定管理员"},{default:l(()=>[e(_,{modelValue:s.search.designated_admin,"onUpdate:modelValue":a[16]||(a[16]=t=>s.search.designated_admin=t),placeholder:"请输入指定管理员",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(f,{span:6},{default:l(()=>[e(i,{label:"管理状态"},{default:l(()=>[e(h,{modelValue:s.search.management_status,"onUpdate:modelValue":a[17]||(a[17]=t=>s.search.management_status=t),placeholder:"请选择管理状态",clearable:"",class:"form-control"},{default:l(()=>[e(p,{label:"所有",value:""}),e(p,{label:"有人管",value:"有人管"}),e(p,{label:"无人管",value:"无人管"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(f,{span:6},{default:l(()=>[e(i,{label:"是否虚拟机"},{default:l(()=>[e(h,{modelValue:s.search.is_virtual_machine,"onUpdate:modelValue":a[18]||(a[18]=t=>s.search.is_virtual_machine=t),placeholder:"请选择是否虚拟机",clearable:"",class:"form-control"},{default:l(()=>[e(p,{label:"所有",value:""}),e(p,{label:"是",value:"是"}),e(p,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(f,{span:6},{default:l(()=>[e(i,{label:"是否在线"},{default:l(()=>[e(h,{modelValue:s.search.online_status,"onUpdate:modelValue":a[19]||(a[19]=t=>s.search.online_status=t),placeholder:"请选择是否在线",clearable:"",class:"form-control"},{default:l(()=>[e(p,{label:"所有",value:""}),e(p,{label:"在线",value:"在线"}),e(p,{label:"离线",value:"离线"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(f,{span:24,class:"search-buttons-col"},{default:l(()=>[e(i,{label:" ",class:"form-item-with-label search-buttons"},{default:l(()=>[d("div",oe,[e(g,{type:"primary",onClick:c.loadData},{default:l(()=>[e(y,null,{default:l(()=>[e(U)]),_:1}),u("查询 ")]),_:1},8,["onClick"]),e(g,{onClick:c.resetSearch},{default:l(()=>[u("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),d("div",re,[d("div",ne,[e(g,{type:"success",disabled:!s.hasInsertPermission,onClick:c.handleAdd},{default:l(()=>[e(y,null,{default:l(()=>[e(z)]),_:1}),u("新增资产 ")]),_:1},8,["disabled","onClick"])]),d("div",ie,[e(g,{type:"info",onClick:c.exportData},{default:l(()=>[e(y,null,{default:l(()=>[e(F)]),_:1}),u(" 导出数据 ")]),_:1},8,["onClick"])])]),e(S,{class:"table-card"},{default:l(()=>[G((b(),w(E,{data:s.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",class:"host-scan-table",onSortChange:c.handleSortChange},{default:l(()=>[e(m,{prop:"management_ip",label:"IP地址",sortable:""}),e(m,{prop:"function_purpose",label:"功能用途",sortable:""}),e(m,{prop:"admin1",label:"管理员1",sortable:""}),e(m,{prop:"admin2",label:"管理员2",sortable:""}),e(m,{prop:"designated_admin",label:"指定管理员",sortable:""}),e(m,{prop:"datacenter",label:"所属机房",sortable:""}),e(m,{prop:"management_status",label:"管理状态",sortable:""}),e(m,{prop:"is_virtual_machine",label:"是否虚拟机",sortable:""}),e(m,{prop:"online_status",label:"是否在线",sortable:""},{default:l(t=>[e(L,{type:t.row.online_status==="在线"?"success":"danger"},{default:l(()=>[u(k(t.row.online_status),1)]),_:2},1032,["type"])]),_:1}),e(m,{prop:"remarks",label:"备注",sortable:""},{default:l(t=>[d("span",null,k(t.row.remarks),1)]),_:1}),e(m,{prop:"created_at",label:"创建时间",sortable:""}),e(m,{prop:"created_by",label:"创建人",sortable:""}),e(m,{prop:"updated_at",label:"更新时间",sortable:""}),e(m,{prop:"updated_by",label:"更新人",sortable:""}),e(m,{label:"操作",fixed:"right"},{default:l(t=>[d("div",de,[e(g,{size:"small",type:"warning",disabled:!s.hasUpdatePermission,onClick:R=>c.handleEdit(t.$index,t.row)},{default:l(()=>[u("编辑")]),_:2},1032,["disabled","onClick"]),e(g,{size:"small",type:"danger",disabled:!s.hasDeletePermission,onClick:R=>c.handleDelete(t.$index,t.row)},{default:l(()=>[u("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[B,s.loading]]),d("div",me,[e(q,{background:"","current-page":s.search.currentPage,"page-size":s.search.pageSize,total:s.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:c.handlePageSizeChange,onCurrentChange:c.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const _e=N(W,[["render",ue],["__scopeId","data-v-4f6aeea1"]]);export{_e as default};
