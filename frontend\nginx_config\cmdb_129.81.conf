# 定义缓存路径和参数
proxy_cache_path /var/cache/nginx/cmdb levels=1:2 keys_zone=cmdb_cache:10m max_size=1g inactive=60m;
proxy_temp_path /var/cache/nginx/temp;

# 定义浏览器缓存映射
map $sent_http_content_type $expires {
    default                    off;
    text/html                  epoch;  # 不缓存HTML
    text/css                   max;    # CSS文件长期缓存
    application/javascript     max;    # JS文件长期缓存
    ~image/                    max;    # 图片长期缓存
    ~font/                     max;    # 字体长期缓存
}

# CMDB前端服务器 - 9000端口
server {
    listen       9000;
    server_name  *************;

    # 启用浏览器缓存
    expires $expires;

    # 前端静态文件
    location / {
        root /home/<USER>/frontend/dist;
        try_files $uri $uri/ /index.html;
        index index.html;

        # 添加安全相关的HTTP头
        add_header X-Content-Type-Options nosniff;
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-XSS-Protection "1; mode=block";

        # 启用访问控制
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization";
    }

    # 静态资源优化
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        root /home/<USER>/frontend/dist;
        expires max;
        add_header Cache-Control "public, max-age=31536000, immutable";
        access_log off;  # 不记录静态资源访问日志，减少I/O

        # 启用Nginx缓存
        proxy_cache cmdb_cache;
        proxy_cache_valid 200 302 1d;
        proxy_cache_valid 404 1m;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        proxy_cache_lock on;
        add_header X-Cache-Status $upstream_cache_status;
    }

    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3000/api/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection "";  # 启用HTTP keepalive

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 不缓存API响应
        proxy_cache_bypass $http_pragma $http_authorization;
        proxy_no_cache $http_pragma $http_authorization;

        # 启用CORS
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }

    # 文件下载代理
    location /files/ {
        proxy_pass http://127.0.0.1:3000/files/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection "";  # 启用HTTP keepalive

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 启用文件缓存
        proxy_cache cmdb_cache;
        proxy_cache_valid 200 302 1d;
        proxy_cache_valid 404 1m;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        proxy_cache_lock on;
        add_header X-Cache-Status $upstream_cache_status;

        # 启用CORS
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# 80端口服务器 - 负载均衡入口
server {
    listen              80;
    server_name         *************;

    # 启用压缩
    gzip on;
    gzip_min_length 1000;
    gzip_proxied any;

    # 启用浏览器缓存
    expires $expires;

    # 所有请求转发到后端服务器
    location / {
        proxy_pass http://cmdb-web;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection "";  # 启用HTTP keepalive

        # 添加主机标识
        proxy_set_header X-Primary-Server "true";

        # 增加超时设置，避免请求挂起
        proxy_connect_timeout 120s;  # 增加到120秒
        proxy_send_timeout 120s;     # 增加到120秒
        proxy_read_timeout 120s;     # 增加到120秒

        # 增加缓冲区大小，处理大请求
        proxy_buffer_size 16k;
        proxy_buffers 4 32k;
        proxy_busy_buffers_size 64k;

        # 健康检查
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;

        # 错误处理
        proxy_intercept_errors on;

        # 启用缓存，但排除API请求和登录页面
        proxy_cache cmdb_cache;
        proxy_cache_valid 200 302 10m;
        proxy_cache_valid 404 1m;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        proxy_cache_lock on;
        proxy_cache_bypass $http_pragma $http_authorization $request_uri;
        proxy_no_cache $http_pragma $http_authorization $request_uri ~*/login;
        add_header X-Cache-Status $upstream_cache_status;
    }

    # 专门处理API请求
    location /api/ {
        proxy_pass http://cmdb-web/api/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection "";  # 启用HTTP keepalive

        # 添加主机标识
        proxy_set_header X-Primary-Server "true";

        # 增加超时设置，避免请求挂起
        proxy_connect_timeout 120s;  # 增加到120秒
        proxy_send_timeout 120s;     # 增加到120秒
        proxy_read_timeout 120s;     # 增加到120秒

        # 增加缓冲区大小，处理大请求
        proxy_buffer_size 16k;
        proxy_buffers 4 32k;
        proxy_busy_buffers_size 64k;

        # 不缓存API响应
        proxy_cache_bypass $http_pragma $http_authorization;
        proxy_no_cache $http_pragma $http_authorization;

        # 错误处理
        proxy_intercept_errors on;
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
    }

    # 文件下载代理
    location /files/ {
        proxy_pass http://cmdb-web/files/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection "";  # 启用HTTP keepalive

        # 添加主机标识
        proxy_set_header X-Primary-Server "true";

        # 增加超时设置，避免请求挂起
        proxy_connect_timeout 120s;  # 增加到120秒
        proxy_send_timeout 120s;     # 增加到120秒
        proxy_read_timeout 120s;     # 增加到120秒

        # 增加缓冲区大小，处理大文件
        proxy_buffer_size 16k;
        proxy_buffers 4 32k;
        proxy_busy_buffers_size 64k;

        # 启用文件缓存
        proxy_cache cmdb_cache;
        proxy_cache_valid 200 302 1d;
        proxy_cache_valid 404 1m;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        proxy_cache_lock on;
        add_header X-Cache-Status $upstream_cache_status;

        # 错误处理
        proxy_intercept_errors on;
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
    }

    # 静态资源优化
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://cmdb-web;
        expires max;
        add_header Cache-Control "public, max-age=31536000, immutable";
        access_log off;

        # 启用缓存
        proxy_cache cmdb_cache;
        proxy_cache_valid 200 302 30d;
        proxy_cache_valid 404 1m;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        proxy_cache_lock on;
        add_header X-Cache-Status $upstream_cache_status;
    }

    # 健康检查端点
    location /health {
        access_log off;
        return 200 "OK";
    }
}

# 后端服务器组 - 负载均衡配置
upstream cmdb-web {
    # 使用IP哈希算法，确保同一客户端总是访问同一服务器
    ip_hash;

    # 服务器列表
    server *************:9000 weight=5 max_fails=3 fail_timeout=30s;
    # server *************:9000 weight=5 max_fails=3 fail_timeout=30s ;

    # 启用keepalive连接
    keepalive 32;

}
