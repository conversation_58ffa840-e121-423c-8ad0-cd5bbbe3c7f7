import{_ as N,z as O,v as j,A as K,c as D,a as e,f as g,w as o,b as d,F as y,l as V,h as u,B as L,C as G,m as w,x as H,t as U,e as h}from"./index-Bo9zKuAo.js";import{u as S,w as J,F as Q}from"./FileSaver.min-BlIK9FxR.js";const W={components:{Plus:K,Search:j,Download:O},data(){var s,a,i;return{userArr:[],loading:!1,operatingsystems:[],datacenter1s:[],hasDeletePermission:(s=localStorage.getItem("role_code"))==null?void 0:s.includes("D"),hasUpdatePermission:(a=localStorage.getItem("role_code"))==null?void 0:a.includes("U"),hasInsertPermission:(i=localStorage.getItem("role_code"))==null?void 0:i.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{management_ip:"",hostname:"",admin1:"",admin2:"",host_ip:"",data_center1:"",app_system_id:"",is_monitored:"",online_status:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,management_ip:"",hostname:"",function_purpose:"",admin1:"",admin2:"",host_ip:"",operating_system:"",data_center1:"",app_system_id:"",virtual_host_ip:"",data_center2:"",is_monitored:"是",weak_password_exists:"否",weak_password_correction_date:null},rules:{management_ip:[{required:!0,message:"请输入管理IP",trigger:"blur"},{pattern:/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,message:"请输入正确的IP地址格式",trigger:"blur"}],hostname:[{required:!0,message:"请输入主机名",trigger:"blur"},{min:2,max:50,message:"主机名长度应在2-50个字符之间",trigger:"blur"}],function_purpose:[{required:!0,message:"请输入功能用途",trigger:"blur"}],admin1:[{required:!0,message:"请输入管理员1",trigger:"blur"}],operating_system:[{required:!0,message:"请选择操作系统",trigger:"change"}]}}},mounted(){this.$route.query.search_ip&&(this.search.management_ip=this.$route.query.search_ip),this.loadData(),this.getDatadict("K","operatingsystems"),this.getDatadict("A","datacenter1s"),this.$route.query.from_discovery==="true"&&this.$nextTick(()=>{this.handleAddFromDiscovery()})},methods:{handlePageChange(s){this.search.currentPage=s,this.loadData()},handlePageSizeChange(s){this.search.pageSize=parseInt(s),this.search.currentPage=1,this.loadData()},handleSortChange({prop:s,order:a}){this.search.sortProp=s,this.search.sortOrder=a==="ascending"?"asc":"desc",this.loadData()},async loadData(){try{this.loading=!0;const s=await this.$axios.post("/api/get_cmdb_vm_registry",this.search);this.userArr=s.data.msg,this.search.total=s.data.total}catch(s){console.error("数据加载失败:",s),this.$message.error("数据加载失败")}finally{this.loading=!1}},async validateAndSubmitAdd(){try{await this.$refs.addFormRef.validate(),await this.submitAdd()}catch{this.$message.error("请完善必填项后再提交")}},async submitAdd(){var s,a;try{const i={management_ip:this.formData.management_ip,hostname:this.formData.hostname,function_purpose:this.formData.function_purpose,admin1:this.formData.admin1,admin2:this.formData.admin2||"",host_ip:this.formData.host_ip||"",operating_system:this.formData.operating_system,data_center1:this.formData.data_center1||"",admin:"",app_system_id:"",virtual_host_ip:"",data_center2:"",is_monitored:"是",weak_password_exists:this.formData.weak_password_exists||"否",weak_password_correction_date:this.formData.weak_password_correction_date,usernameby:localStorage.getItem("loginUsername")||"unknown"};console.log("提交的数据:",i),await this.$axios.post("/api/add_cmdb_vm_registry",i),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(i){console.error("添加失败:",i),this.$message.error(((a=(s=i.response)==null?void 0:s.data)==null?void 0:a.msg)||"添加失败")}},async validateAndSubmitEdit(){try{await this.$refs.editFormRef.validate(),await this.submitEdit()}catch{this.$message.error("请完善必填项后再提交")}},async submitEdit(){var s,a;try{const i={id:this.formData.id,management_ip:this.formData.management_ip,hostname:this.formData.hostname,function_purpose:this.formData.function_purpose,admin1:this.formData.admin1,admin2:this.formData.admin2||"",host_ip:this.formData.host_ip||"",operating_system:this.formData.operating_system,data_center1:this.formData.data_center1||"",admin:"",app_system_id:"",virtual_host_ip:"",data_center2:"",is_monitored:"是",weak_password_exists:this.formData.weak_password_exists||"否",weak_password_correction_date:this.formData.weak_password_correction_date,usernameby:localStorage.getItem("loginUsername")||"unknown"};console.log("提交的编辑数据:",i),await this.$axios.post("/api/update_cmdb_vm_registry",i),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(i){console.error("更新失败:",i),this.$message.error(((a=(s=i.response)==null?void 0:s.data)==null?void 0:a.msg)||"更新失败")}},async submitDelete(){var s,a;try{const i={...this.formData,usernameby:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/del_cmdb_vm_registry",i),this.$message.success("删除成功"),this.loadData(),this.dialogVisible.delete=!1}catch(i){console.error("删除失败:",i),this.$message.error(((a=(s=i.response)==null?void 0:s.data)==null?void 0:a.msg)||"删除失败")}},resetSearch(){this.search={management_ip:"",hostname:"",admin1:"",admin2:"",host_ip:"",operating_system:"",data_center1:"",is_monitored:"",online_status:"",weak_password_exists:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},async getDatadict(s,a){try{const i=await this.$axios.post("/api/get_cmdb_data_dictionary",{dict_code:s});this[a]=i.data.msg}catch(i){console.error("数据加载失败:",i),this.$message.error("数据加载失败")}},handleAdd(){this.dialogVisible.add=!this.dialogVisible.add,this.formData={is_monitored:"是",weak_password_exists:"否"},this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields()})},handleEdit(s,a){this.dialogVisible.edit=!0,this.formData.id=a.id,this.formData.management_ip=a.management_ip,this.formData.hostname=a.hostname,this.formData.function_purpose=a.function_purpose,this.formData.admin1=a.admin1,this.formData.admin2=a.admin2,this.formData.host_ip=a.host_ip,this.formData.operating_system=a.operating_system,this.formData.data_center1=a.data_center1,this.formData.weak_password_exists=a.weak_password_exists,this.formData.weak_password_correction_date=a.weak_password_correction_date,this.$refs.editFormRef&&this.$nextTick(()=>{this.$refs.editFormRef.clearValidate()})},handleDelete(s,a){this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=a.id,this.formData.management_ip=a.management_ip},exportData(){const a=this.$refs.table.columns,i=a.map(c=>c.label),I=this.userArr.map(c=>a.map(k=>c[k.property])),l=[i,...I],_=S.aoa_to_sheet(l),n=S.book_new();S.book_append_sheet(n,_,"Sheet1");const r=J(n,{bookType:"xlsx",type:"array"}),m=new Blob([r],{type:"application/octet-stream"});Q.saveAs(m,"虚拟机信息.xlsx")},handleAddFromDiscovery(){this.dialogVisible.add=!0;const{ip_address:s,hostname:a,open_ports:i}=this.$route.query;this.formData={management_ip:s||"",hostname:a||"",function_purpose:"",admin1:"",admin2:"",host_ip:"",operating_system:"",data_center1:"",is_monitored:"",weak_password_exists:"",remarks:i?`开放端口: ${i}`:""},this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields(),this.$message.info("请完善资产信息并提交")}),this.$router.replace({path:this.$route.path})}}},X={class:"user-manage"},Z={class:"dialogdiv"},$={class:"dialog-footer"},ee={class:"dialogdiv"},ae={class:"dialog-footer"},te={class:"button-container"},le={class:"action-bar unified-action-bar"},oe={class:"action-bar-left"},se={class:"action-bar-right"},re={style:{display:"flex","white-space":"nowrap"}},ie={class:"pagination"};function ne(s,a,i,I,l,_){const n=d("el-input"),r=d("el-form-item"),m=d("el-option"),c=d("el-select"),k=d("el-date-picker"),v=d("el-form"),f=d("el-button"),x=d("el-dialog"),F=d("el-alert"),b=d("el-col"),Y=d("Search"),C=d("el-icon"),z=d("el-row"),A=d("el-card"),q=d("Plus"),R=d("Download"),p=d("el-table-column"),P=d("el-tag"),B=d("el-table"),E=d("el-pagination"),M=G("loading");return h(),D("div",X,[e(x,{modelValue:l.dialogVisible.add,"onUpdate:modelValue":a[11]||(a[11]=t=>l.dialogVisible.add=t),title:"新增虚拟机信息",width:"400","align-center":""},{footer:o(()=>[g("div",$,[e(f,{onClick:a[10]||(a[10]=t=>l.dialogVisible.add=!1)},{default:o(()=>[u("返回")]),_:1}),e(f,{type:"primary",onClick:_.validateAndSubmitAdd},{default:o(()=>[u("确定")]),_:1},8,["onClick"])])]),default:o(()=>[g("div",Z,[e(v,{model:l.formData,rules:l.rules,ref:"addFormRef","label-position":"right","status-icon":""},{default:o(()=>[e(r,{prop:"management_ip",label:"管理IP:"},{default:o(()=>[e(n,{modelValue:l.formData.management_ip,"onUpdate:modelValue":a[0]||(a[0]=t=>l.formData.management_ip=t),style:{width:"240px"},clearable:"",placeholder:"请输入管理IP"},null,8,["modelValue"])]),_:1}),e(r,{prop:"hostname",label:"主机名:"},{default:o(()=>[e(n,{modelValue:l.formData.hostname,"onUpdate:modelValue":a[1]||(a[1]=t=>l.formData.hostname=t),style:{width:"240px"},clearable:"",placeholder:"请输入主机名"},null,8,["modelValue"])]),_:1}),e(r,{prop:"function_purpose",label:"功能用途:"},{default:o(()=>[e(n,{modelValue:l.formData.function_purpose,"onUpdate:modelValue":a[2]||(a[2]=t=>l.formData.function_purpose=t),style:{width:"240px"},type:"textarea",placeholder:"请输入功能用途"},null,8,["modelValue"])]),_:1}),e(r,{prop:"admin1",label:"管理员1:"},{default:o(()=>[e(n,{modelValue:l.formData.admin1,"onUpdate:modelValue":a[3]||(a[3]=t=>l.formData.admin1=t),style:{width:"240px"},clearable:"",placeholder:"请输入管理员1"},null,8,["modelValue"])]),_:1}),e(r,{prop:"admin2",label:"管理员2:"},{default:o(()=>[e(n,{modelValue:l.formData.admin2,"onUpdate:modelValue":a[4]||(a[4]=t=>l.formData.admin2=t),style:{width:"240px"},clearable:"",placeholder:"请输入管理员2"},null,8,["modelValue"])]),_:1}),e(r,{prop:"host_ip",label:"宿主机IP:"},{default:o(()=>[e(n,{modelValue:l.formData.host_ip,"onUpdate:modelValue":a[5]||(a[5]=t=>l.formData.host_ip=t),style:{width:"240px"},clearable:"",placeholder:"请输入宿主机IP"},null,8,["modelValue"])]),_:1}),e(r,{prop:"operating_system",label:"操作系统:"},{default:o(()=>[e(c,{modelValue:l.formData.operating_system,"onUpdate:modelValue":a[6]||(a[6]=t=>l.formData.operating_system=t),style:{width:"240px"},placeholder:"请选择操作系统"},{default:o(()=>[(h(!0),D(y,null,V(l.operatingsystems,t=>(h(),w(m,{key:t.dict_code,label:t.dict_name,value:t.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"data_center1",label:"所属机房:"},{default:o(()=>[e(c,{modelValue:l.formData.data_center1,"onUpdate:modelValue":a[7]||(a[7]=t=>l.formData.data_center1=t),style:{width:"240px"},placeholder:"请选择所属机房"},{default:o(()=>[(h(!0),D(y,null,V(l.datacenter1s,t=>(h(),w(m,{key:t.dict_code,label:t.dict_name,value:t.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"weak_password_exists",label:"是否存在弱密码:"},{default:o(()=>[e(c,{modelValue:l.formData.weak_password_exists,"onUpdate:modelValue":a[8]||(a[8]=t=>l.formData.weak_password_exists=t),style:{width:"240px"},placeholder:"请选择状态"},{default:o(()=>[e(m,{label:"是",value:"是"}),e(m,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"weak_password_correction_date",label:"弱密码修正时间:"},{default:o(()=>[e(k,{modelValue:l.formData.weak_password_correction_date,"onUpdate:modelValue":a[9]||(a[9]=t=>l.formData.weak_password_correction_date=t),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"240px"},placeholder:"选择日期"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),e(x,{modelValue:l.dialogVisible.edit,"onUpdate:modelValue":a[23]||(a[23]=t=>l.dialogVisible.edit=t),title:"编辑虚拟机信息",width:"400","align-center":""},{footer:o(()=>[g("div",ae,[e(f,{onClick:a[22]||(a[22]=t=>l.dialogVisible.edit=!1)},{default:o(()=>[u("取消")]),_:1}),e(f,{type:"primary",onClick:_.validateAndSubmitEdit},{default:o(()=>[u("更新")]),_:1},8,["onClick"])])]),default:o(()=>[g("div",ee,[e(v,{model:l.formData,rules:l.rules,ref:"editFormRef","label-position":"right","status-icon":""},{default:o(()=>[e(r,{prop:"management_ip",label:"管理IP:"},{default:o(()=>[e(n,{modelValue:l.formData.management_ip,"onUpdate:modelValue":a[12]||(a[12]=t=>l.formData.management_ip=t),style:{width:"240px"},clearable:"",placeholder:"请输入管理IP"},null,8,["modelValue"])]),_:1}),e(r,{prop:"hostname",label:"主机名:"},{default:o(()=>[e(n,{modelValue:l.formData.hostname,"onUpdate:modelValue":a[13]||(a[13]=t=>l.formData.hostname=t),style:{width:"240px"},clearable:"",placeholder:"请输入主机名"},null,8,["modelValue"])]),_:1}),e(r,{prop:"function_purpose",label:"功能用途:"},{default:o(()=>[e(n,{modelValue:l.formData.function_purpose,"onUpdate:modelValue":a[14]||(a[14]=t=>l.formData.function_purpose=t),style:{width:"240px"},type:"textarea",placeholder:"请输入功能用途"},null,8,["modelValue"])]),_:1}),e(r,{prop:"admin1",label:"管理员1:"},{default:o(()=>[e(n,{modelValue:l.formData.admin1,"onUpdate:modelValue":a[15]||(a[15]=t=>l.formData.admin1=t),style:{width:"240px"},clearable:"",placeholder:"请输入管理员1"},null,8,["modelValue"])]),_:1}),e(r,{prop:"admin2",label:"管理员2:"},{default:o(()=>[e(n,{modelValue:l.formData.admin2,"onUpdate:modelValue":a[16]||(a[16]=t=>l.formData.admin2=t),style:{width:"240px"},clearable:"",placeholder:"请输入管理员2"},null,8,["modelValue"])]),_:1}),e(r,{prop:"host_ip",label:"宿主机IP:"},{default:o(()=>[e(n,{modelValue:l.formData.host_ip,"onUpdate:modelValue":a[17]||(a[17]=t=>l.formData.host_ip=t),style:{width:"240px"},clearable:"",placeholder:"请输入宿主机IP"},null,8,["modelValue"])]),_:1}),e(r,{prop:"operating_system",label:"操作系统:"},{default:o(()=>[e(c,{modelValue:l.formData.operating_system,"onUpdate:modelValue":a[18]||(a[18]=t=>l.formData.operating_system=t),style:{width:"240px"},placeholder:"请选择操作系统"},{default:o(()=>[(h(!0),D(y,null,V(l.operatingsystems,t=>(h(),w(m,{key:t.dict_code,label:t.dict_name,value:t.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"data_center1",label:"所属机房:"},{default:o(()=>[e(c,{modelValue:l.formData.data_center1,"onUpdate:modelValue":a[19]||(a[19]=t=>l.formData.data_center1=t),style:{width:"240px"},placeholder:"请选择所属机房"},{default:o(()=>[(h(!0),D(y,null,V(l.datacenter1s,t=>(h(),w(m,{key:t.dict_code,label:t.dict_name,value:t.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"weak_password_exists",label:"是否存在弱密码:"},{default:o(()=>[e(c,{modelValue:l.formData.weak_password_exists,"onUpdate:modelValue":a[20]||(a[20]=t=>l.formData.weak_password_exists=t),style:{width:"240px"},placeholder:"请选择状态"},{default:o(()=>[e(m,{label:"是",value:"是"}),e(m,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"weak_password_correction_date",label:"弱密码修正时间:"},{default:o(()=>[e(k,{modelValue:l.formData.weak_password_correction_date,"onUpdate:modelValue":a[21]||(a[21]=t=>l.formData.weak_password_correction_date=t),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"240px"},placeholder:"选择日期"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),e(x,{modelValue:l.dialogVisible.delete,"onUpdate:modelValue":a[25]||(a[25]=t=>l.dialogVisible.delete=t),title:"删除管理IP",width:"500","align-center":""},{footer:o(()=>[g("div",null,[e(f,{onClick:a[24]||(a[24]=t=>l.dialogVisible.delete=!1)},{default:o(()=>[u("取消")]),_:1}),e(f,{type:"danger",onClick:_.submitDelete},{default:o(()=>[u("确认删除")]),_:1},8,["onClick"])])]),default:o(()=>[e(F,{type:"warning",title:`确定要删除 IP 为 ${l.formData.management_ip} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),e(A,{class:"search-card"},{default:o(()=>[e(v,{inline:!0},{default:o(()=>[e(z,{gutter:10},{default:o(()=>[e(b,{span:6},{default:o(()=>[e(r,{label:"管理IP"},{default:o(()=>[e(n,{modelValue:l.search.management_ip,"onUpdate:modelValue":a[26]||(a[26]=t=>l.search.management_ip=t),placeholder:"请输入管理IP",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:o(()=>[e(r,{label:"宿主机IP"},{default:o(()=>[e(n,{modelValue:l.search.host_ip,"onUpdate:modelValue":a[27]||(a[27]=t=>l.search.host_ip=t),placeholder:"请输入宿主机IP",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:o(()=>[e(r,{label:"主机名"},{default:o(()=>[e(n,{modelValue:l.search.hostname,"onUpdate:modelValue":a[28]||(a[28]=t=>l.search.hostname=t),placeholder:"请输入主机名",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:o(()=>[e(r,{label:"虚拟机所属系统"},{default:o(()=>[e(n,{modelValue:l.search.app_system_id,"onUpdate:modelValue":a[29]||(a[29]=t=>l.search.app_system_id=t),placeholder:"请输入虚拟机所属系统",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:o(()=>[e(r,{label:"管理员1"},{default:o(()=>[e(n,{modelValue:l.search.admin1,"onUpdate:modelValue":a[30]||(a[30]=t=>l.search.admin1=t),placeholder:"请输入管理员1",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:o(()=>[e(r,{label:"管理员2"},{default:o(()=>[e(n,{modelValue:l.search.admin2,"onUpdate:modelValue":a[31]||(a[31]=t=>l.search.admin2=t),placeholder:"请输入管理员2",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:o(()=>[e(r,{label:"所属机房"},{default:o(()=>[e(c,{modelValue:l.search.data_center1,"onUpdate:modelValue":a[32]||(a[32]=t=>l.search.data_center1=t),placeholder:"请选择所属机房",class:"form-control"},{default:o(()=>[e(m,{label:"所有",value:""}),(h(!0),D(y,null,V(l.datacenter1s,t=>(h(),w(m,{key:t.dict_code,label:t.dict_name,value:t.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:o(()=>[e(r,{label:"是否监控"},{default:o(()=>[e(c,{modelValue:l.search.is_monitored,"onUpdate:modelValue":a[33]||(a[33]=t=>l.search.is_monitored=t),placeholder:"请选择是否监控",class:"form-control"},{default:o(()=>[e(m,{label:"所有",value:""}),e(m,{label:"是",value:"是"}),e(m,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:o(()=>[e(r,{label:"是否在线"},{default:o(()=>[e(c,{modelValue:l.search.online_status,"onUpdate:modelValue":a[34]||(a[34]=t=>l.search.online_status=t),placeholder:"请选择是否在线",class:"form-control"},{default:o(()=>[e(m,{label:"所有",value:""}),e(m,{label:"在线",value:"在线"}),e(m,{label:"离线",value:"离线"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(b,{span:18,class:"search-buttons-col"},{default:o(()=>[e(r,{label:" ",class:"form-item-with-label search-buttons"},{default:o(()=>[g("div",te,[e(f,{type:"primary",onClick:_.loadData},{default:o(()=>[e(C,null,{default:o(()=>[e(Y)]),_:1}),u("查询 ")]),_:1},8,["onClick"]),e(f,{onClick:_.resetSearch},{default:o(()=>[u("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),g("div",le,[g("div",oe,[e(f,{type:"success",disabled:!l.hasInsertPermission,onClick:_.handleAdd},{default:o(()=>[e(C,null,{default:o(()=>[e(q)]),_:1}),u("新增资产 ")]),_:1},8,["disabled","onClick"])]),g("div",se,[e(f,{type:"info",onClick:_.exportData},{default:o(()=>[e(C,null,{default:o(()=>[e(R)]),_:1}),u(" 导出数据 ")]),_:1},8,["onClick"])])]),e(A,{class:"table-card"},{default:o(()=>[L((h(),w(B,{data:l.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:_.handleSortChange},{default:o(()=>[H("",!0),e(p,{prop:"management_ip",label:"管理IP",sortable:""}),e(p,{prop:"hostname",label:"主机名",sortable:""}),e(p,{prop:"function_purpose",label:"功能用途",sortable:""}),e(p,{prop:"admin1",label:"管理员1",sortable:""}),e(p,{prop:"admin2",label:"管理员2",sortable:""}),e(p,{prop:"host_ip",label:"宿主机IP",sortable:""}),e(p,{prop:"operating_system",label:"操作系统",sortable:""}),e(p,{prop:"data_center1",label:"所属机房",sortable:""}),e(p,{prop:"app_system_id",label:"虚拟机所属系统",sortable:""}),e(p,{prop:"is_monitored",label:"是否监控",sortable:""},{default:o(t=>[e(P,{type:t.row.is_monitored==="是"?"success":"danger"},{default:o(()=>[u(U(t.row.is_monitored),1)]),_:2},1032,["type"])]),_:1}),e(p,{prop:"online_status",label:"是否在线",sortable:""},{default:o(t=>[e(P,{type:t.row.online_status==="在线"?"success":"danger"},{default:o(()=>[u(U(t.row.online_status),1)]),_:2},1032,["type"])]),_:1}),e(p,{prop:"weak_password_exists",label:"是否存在弱密码",sortable:""},{default:o(t=>[e(P,{type:t.row.weak_password_exists==="是"?"danger":"success"},{default:o(()=>[u(U(t.row.weak_password_exists),1)]),_:2},1032,["type"])]),_:1}),e(p,{prop:"weak_password_correction_date",label:"弱密码修正时间",sortable:""}),e(p,{prop:"created_at",label:"创建时间",sortable:""}),e(p,{prop:"created_by",label:"创建人",sortable:""}),e(p,{prop:"updated_at",label:"更新时间",sortable:""}),e(p,{prop:"updated_by",label:"更新人",sortable:""}),e(p,{label:"操作",fixed:"right"},{default:o(t=>[g("div",re,[e(f,{size:"small",type:"warning",disabled:!l.hasUpdatePermission,onClick:T=>_.handleEdit(t.$index,t.row)},{default:o(()=>[u("编辑")]),_:2},1032,["disabled","onClick"]),e(f,{size:"small",type:"danger",disabled:!l.hasDeletePermission,onClick:T=>_.handleDelete(t.$index,t.row)},{default:o(()=>[u("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[M,l.loading]]),g("div",ie,[e(E,{background:"","current-page":l.search.currentPage,"page-size":l.search.pageSize,total:l.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:_.handlePageSizeChange,onCurrentChange:_.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const pe=N(W,[["render",ne],["__scopeId","data-v-e14dd5bf"]]);export{pe as default};
