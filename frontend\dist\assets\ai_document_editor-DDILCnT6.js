import{_ as g,a1 as T,a2 as b,c as r,f as e,a as o,w as n,h as L,b as t,x as k,p as v,q as w,e as l}from"./index-Bo9zKuAo.js";const I={name:"DocumentEditor",components:{ArrowLeft:b,Loading:T},data(){return{loading:!0,iframeUrl:"http://10.242.194.92/chatbot/QnnVm8tWug1TqY77",loadTimeout:null}},mounted(){this.loadTimeout=setTimeout(()=>{this.loading=!1},1e4),this.$message({message:"正在加载文档智能修订工具，请稍候...",type:"info",duration:3e3})},beforeUnmount(){this.loadTimeout&&clearTimeout(this.loadTimeout)},methods:{goBack(){this.$router.push("/ai_platform")},handleIframeLoaded(){this.loading=!1,this.loadTimeout&&clearTimeout(this.loadTimeout),this.$message({message:"文档智能修订工具加载完成",type:"success",duration:2e3})}}},_=a=>(v("data-v-b5dabc04"),a=a(),w(),a),x={class:"document-editor"},y={class:"editor-header"},B=_(()=>e("h2",{class:"page-title"},"文档智能修订",-1)),C={class:"header-actions"},V={class:"editor-content"},A={key:0,class:"loading-container"},N={class:"loading-text"},S=_(()=>e("span",null,"文档智能修订工具加载中，请稍候...",-1)),U=["src"];function q(a,d,E,$,i,s){const m=t("ArrowLeft"),c=t("el-icon"),u=t("el-button"),h=t("el-skeleton"),f=t("Loading");return l(),r("div",x,[e("div",y,[B,e("div",C,[o(u,{type:"primary",onClick:s.goBack},{default:n(()=>[o(c,null,{default:n(()=>[o(m)]),_:1}),L(" 返回AI平台 ")]),_:1},8,["onClick"])])]),e("div",V,[i.loading?(l(),r("div",A,[o(h,{rows:6,animated:""}),e("div",N,[o(c,{class:"loading-icon"},{default:n(()=>[o(f)]),_:1}),S])])):k("",!0),e("iframe",{ref:"editorFrame",src:i.iframeUrl,frameborder:"0",allow:"microphone",onLoad:d[0]||(d[0]=(...p)=>s.handleIframeLoaded&&s.handleIframeLoaded(...p))},`
      `,40,U)])])}const F=g(I,[["render",q],["__scopeId","data-v-b5dabc04"]]);export{F as default};
