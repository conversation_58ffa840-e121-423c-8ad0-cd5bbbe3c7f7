-- 为变更管理表添加补充资料字段
-- 版本: 2.2.0.6
-- 日期: 2025年5月26日

-- 检查并添加补充资料字段
DO $$
BEGIN
    -- 检查supplementary_material字段是否存在
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'ops_change_management' 
        AND column_name = 'supplementary_material'
    ) THEN
        -- 添加补充资料字段
        ALTER TABLE public.ops_change_management 
        ADD COLUMN supplementary_material VARCHAR(255);
        
        -- 添加字段注释
        COMMENT ON COLUMN public.ops_change_management.supplementary_material IS '补充资料文件路径';
        
        RAISE NOTICE '已添加supplementary_material字段到ops_change_management表';
    ELSE
        RAISE NOTICE 'supplementary_material字段已存在于ops_change_management表';
    END IF;
END $$;

-- 重新创建变更管理视图，包含新的补充资料字段
DROP VIEW IF EXISTS public.v_ops_change_management;
CREATE OR REPLACE VIEW public.v_ops_change_management
AS SELECT t.id,
    t.change_id,
    t.title,
    t.system,
    t.change_level,
    COALESCE(t2.dict_name, t.change_level) AS change_level_name_display,
    t.planned_change_time,
    (to_char(t.planned_change_time::timestamp with time zone, 'YYYY-MM-DD'::text) || ' '::text) ||
        CASE EXTRACT(dow FROM t.planned_change_time)
            WHEN 0 THEN '星期日'::text
            WHEN 1 THEN '星期一'::text
            WHEN 2 THEN '星期二'::text
            WHEN 3 THEN '星期三'::text
            WHEN 4 THEN '星期四'::text
            WHEN 5 THEN '星期五'::text
            WHEN 6 THEN '星期六'::text
            ELSE NULL::text
        END AS formatted_change_time,
    t.requester,
    u1.real_name AS requester_name,
    t.implementers,
    ( SELECT string_agg(u.real_name::text, ', '::text) AS string_agg
           FROM ( SELECT unnest(string_to_array(t.implementers, ','::text)) AS username) usernames
             LEFT JOIN cmdb_users u ON u.username::text = usernames.username AND u.del_flag::text = '0'::text) AS implementers_name,
    t.description,
    t.oa_process,
    t.oa_process_file,
    t.signed_archive,
    t.signed_archive_file,
    t.operation_sheet,
    t.supplementary_material,
    t.created_at,
    t.created_by,
    t.updated_at,
    t.updated_by
   FROM ops_change_management t
     LEFT JOIN cmdb_data_dictionary t2 ON t2.dict_type::text = 'P'::text AND t2.dict_code::text = t.change_level::text AND t2.del_flag::text = '0'::text
     LEFT JOIN cmdb_users u1 ON u1.username::text = t.requester::text AND u1.del_flag::text = '0'::text
  WHERE t.del_flag = '0'::bpchar;

COMMENT ON VIEW public.v_ops_change_management IS '变更管理视图，包含补充资料字段';
