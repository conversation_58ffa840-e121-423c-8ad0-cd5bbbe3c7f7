import{_ as V,v as N,c as _,f as h,x as g,m as y,a as n,w as r,h as u,b as c,y as F,t as d,F as B,l as D,E as l,p as E,q as K,e as i}from"./index-Bo9zKuAo.js";const O={components:{Search:N},data(){const e=JSON.parse(localStorage.getItem("globalSearchState")||"null");return{search:(e==null?void 0:e.search)||{query:"",total:0,pageSize:10,currentPage:1,loginUsername:localStorage.getItem("loginUsername"),sortProp:"management_ip",sortOrder:"desc"},results:(e==null?void 0:e.results)||[],loading:!1,searchTime:(e==null?void 0:e.searchTime)||0,searchStartTime:0,restoredFromStorage:!!e}},mounted(){this.restoredFromStorage&&this.search.query&&this.results.length===0&&this.handleSearch()},methods:{handlePageChange(e){this.search.currentPage=e,this.handleSearch()},handlePageSizeChange(e){this.search.pageSize=e,this.search.currentPage=1,this.handleSearch()},clearSearch(){this.search.query="",this.results=[],this.search.total=0,this.searchTime=0,localStorage.removeItem("globalSearchState")},async handleSearch(){if(!this.search.query.trim()){l.warning("请输入搜索内容");return}this.loading=!0,this.searchStartTime=performance.now();try{const e=await this.$axios.post("/api/get_global_search",this.search);this.results=e.data.msg||[],this.search.total=e.data.total,this.searchTime=((performance.now()-this.searchStartTime)/1e3).toFixed(2),this.saveSearchState()}catch(e){console.error("搜索失败:",e),l.error("搜索失败，请稍后重试")}finally{this.loading=!1}},saveSearchState(){const e={search:this.search,results:this.results,searchTime:this.searchTime};localStorage.setItem("globalSearchState",JSON.stringify(e))},getTagType(e){return{应用系统信息登记表:"primary",网络设备登记:"success",实体服务器设备登记:"warning",虚拟机登记表:"danger",变更管理:"info"}[e]||"info"},getSystemType(e){return{应用系统信息登记表:"应用系统",网络设备登记:"网络设备",实体服务器设备登记:"实体服务器",虚拟机登记表:"虚拟机",变更管理:"变更管理"}[e]||"其他"},navigateToDetail(e,s){try{if(e.startsWith("http")){window.open(e,"_blank");return}if(!e||typeof e!="string"){console.error("无效的路由路径:",e),l.error("跳转失败，路由路径无效");return}const p={search_ip:s.management_ip};this.$router.push({path:e,query:p})}catch(p){console.error("路由跳转错误:",p,e),l.error("跳转失败，请稍后重试")}},copyIP(e){const s=document.createElement("textarea");s.value=e,s.style.position="fixed",s.style.left="-999999px",s.style.top="-999999px",document.body.appendChild(s),s.focus(),s.select();let p=!1;try{navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(e).then(()=>{l.success("IP已复制到剪贴板")}).catch(()=>{p=document.execCommand("copy"),p?l.success("IP已复制到剪贴板"):l.error("复制失败，请手动复制")}):(p=document.execCommand("copy"),p?l.success("IP已复制到剪贴板"):l.error("复制失败，请手动复制"))}catch(k){console.error("复制过程中出错:",k),l.error("复制失败，请手动复制")}finally{document.body.removeChild(s)}}}},S=e=>(E("data-v-e5787f0f"),e=e(),K(),e),U={class:"search-page"},j={class:"search-container"},J=S(()=>h("h1",{class:"search-title"},"全局资源搜索",-1)),A=S(()=>h("p",{class:"search-subtitle"},"快速查找设备、服务器、应用系统信息或者是变更和事件信息",-1)),L={class:"search-box"},M={key:0,class:"search-stats"},W={key:1,class:"result-container"},G={class:"result-header"},H=["onClick"],Q={class:"result-details"},R={class:"result-actions"},X={key:2,class:"loading-container"},Y=S(()=>h("p",null,"试试其他搜索关键词，或者检查IP地址是否正确",-1));function Z(e,s,p,k,t,o){const m=c("el-button"),C=c("Search"),T=c("el-icon"),b=c("el-input"),x=c("el-tag"),f=c("el-descriptions-item"),I=c("el-descriptions"),w=c("el-skeleton"),z=c("el-empty"),q=c("el-pagination");return i(),_("div",U,[h("div",j,[J,A,h("div",L,[n(b,{modelValue:t.search.query,"onUpdate:modelValue":s[0]||(s[0]=a=>t.search.query=a),placeholder:"请输入IP地址或者是变更编号进行搜索...",onKeyup:F(o.handleSearch,["enter"]),size:"large",clearable:"","prefix-icon":e.Search},{append:r(()=>[t.results.length>0?(i(),y(m,{key:0,type:"danger",onClick:o.clearSearch},{default:r(()=>[u(" 清除 ")]),_:1},8,["onClick"])):(i(),y(m,{key:1,type:"primary",onClick:o.handleSearch},{default:r(()=>[n(T,null,{default:r(()=>[n(C)]),_:1}),u("搜索 ")]),_:1},8,["onClick"]))]),_:1},8,["modelValue","onKeyup","prefix-icon"])]),t.results.length>0?(i(),_("div",M," 找到 "+d(t.search.total)+" 条相关结果 ("+d(t.searchTime)+"秒) ",1)):g("",!0),t.results.length>0?(i(),_("div",W,[(i(!0),_(B,null,D(t.results,(a,P)=>(i(),_("div",{key:P,class:"result-card"},[n(x,{type:o.getTagType(a.menu_name),class:"result-tag",size:"small"},{default:r(()=>[u(d(o.getSystemType(a.menu_name)),1)]),_:2},1032,["type"]),h("div",G,[h("h3",null,[h("a",{href:"javascript:void(0)",onClick:v=>o.navigateToDetail(a.goto_router,a)},d(a.management_ip),9,H)])]),h("div",Q,[n(I,{column:2,border:"",size:"small"},{default:r(()=>[n(f,{label:"数据来源"},{default:r(()=>[u(d(a.menu_name),1)]),_:2},1024),n(f,{label:"关键字"},{default:r(()=>[u(d(a.management_ip),1)]),_:2},1024),n(f,{label:"详情概述"},{default:r(()=>[u(d(a.function_purpose||"暂无详细信息"),1)]),_:2},1024)]),_:2},1024)]),h("div",R,[n(m,{type:"success",link:"",onClick:v=>o.copyIP(a.management_ip)},{default:r(()=>[u(" 复制关键字 ")]),_:2},1032,["onClick"]),n(m,{type:"primary",link:"",onClick:v=>o.navigateToDetail(a.goto_router,a)},{default:r(()=>[u(" 查看详情 ")]),_:2},1032,["onClick"])])]))),128))])):g("",!0),t.loading?(i(),_("div",X,[n(w,{rows:3,animated:""})])):g("",!0),!t.loading&&t.results.length===0&&t.search.query?(i(),y(z,{key:3,description:"未找到相关结果"},{description:r(()=>[Y]),_:1})):g("",!0),t.results.length>0?(i(),y(q,{key:4,class:"pagination",background:"","current-page":t.search.currentPage,"page-size":t.search.pageSize,"page-sizes":[10,20,50,100],total:t.search.total,"pager-count":7,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:o.handlePageSizeChange,onCurrentChange:o.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])):g("",!0)])])}const ee=V(O,[["render",Z],["__scopeId","data-v-e5787f0f"]]);export{ee as default};
