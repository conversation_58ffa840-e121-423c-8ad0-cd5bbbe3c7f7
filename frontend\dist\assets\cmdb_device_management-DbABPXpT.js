import{_ as N,z as O,v as j,A as L,c as h,a as e,f as y,w as o,b as f,F as D,l as v,h as b,B as G,C as H,m as g,x as J,t as F,e as m}from"./index-C0WPPkX4.js";import{u as C,w as K,F as Q}from"./FileSaver.min-BAET_C7X.js";const W={components:{Plus:L,Search:j,Download:O},data(){var d,a,p;return{userArr:[],loading:!1,devicetypes:[],productionattributes:[],datacenters:[],operationstatus:[],hasDeletePermission:(d=localStorage.getItem("role_code"))==null?void 0:d.includes("D"),hasUpdatePermission:(a=localStorage.getItem("role_code"))==null?void 0:a.includes("U"),hasInsertPermission:(p=localStorage.getItem("role_code"))==null?void 0:p.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{management_ip:"",out_of_band_management:null,hostname:null,admin1:null,admin2:null,device_type:null,production_attributes:null,data_center:null,operation_status:null,is_innovative_tech:null,is_monitored:null,online_status:null,year_category:null,serial_number:null,total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,management_ip:null,out_of_band_management:null,hostname:null,function_purpose:null,admin1:null,admin2:null,device_type:null,production_attributes:null,data_center:null,operation_status:null,asset_number:null,purchase_date:null,maintenance_years:null,maintenance_end_date:null,serial_number:null,model:null,version:null,cpu_model:null,is_innovative_tech:null,is_monitored:null,monitoring_ip:null,architecture_mode:null,is_single_point:null,managed_addresses:null,remarks:null,year_category:null,in_monitoring_list:null,pre_monitoring_verified:null,inspection:null},rules:{management_ip:[{required:!0,message:"请输入管理IP",trigger:"blur"},{pattern:/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,message:"请输入正确的IP地址格式",trigger:"blur"}],hostname:[{required:!0,message:"请输入主机名",trigger:"blur"},{min:2,max:50,message:"主机名长度应在2-50个字符之间",trigger:"blur"}],function_purpose:[{required:!0,message:"请输入功能用途",trigger:"blur"}],admin1:[{required:!0,message:"请输入管理员1",trigger:"blur"}],device_type:[{required:!0,message:"请选择设备类型",trigger:"change"}],production_attributes:[{required:!0,message:"请选择生产属性",trigger:"change"}],data_center:[{required:!0,message:"请选择所属机房",trigger:"change"}],operation_status:[{required:!0,message:"请选择生命周期",trigger:"change"}],is_innovative_tech:[{required:!0,message:"请选择是否信创",trigger:"change"}],managed_addresses:[{required:!1,validator:(U,t,_)=>{if(this.formData.is_single_point==="否")if(!t)_(new Error("当设备不是单点时，互备主机IP为必填项"));else{const n=t.split(","),r=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;n.filter(u=>!r.test(u.trim())).length>0?_(new Error("请输入正确的IP地址格式，多个IP请用英文逗号分隔")):_()}else _()},trigger:"blur"}]}}},mounted(){this.$route.query.search_ip&&(this.search.management_ip=this.$route.query.search_ip),this.loadData(),this.getDatadict("B","devicetypes"),this.getDatadict("C","productionattributes"),this.getDatadict("A","datacenters"),this.getDatadict("D","operationstatus"),this.$route.query.from_discovery==="true"&&this.$nextTick(()=>{this.handleAddFromDiscovery()})},methods:{handlePageChange(d){this.search.currentPage=d,this.loadData()},handlePageSizeChange(d){this.search.pageSize=parseInt(d),this.search.currentPage=1,this.loadData()},handleSortChange({prop:d,order:a}){this.search.sortProp=d,this.search.sortOrder=a==="ascending"?"asc":"desc",this.loadData()},async loadData(){try{this.loading=!0;const d=await this.$axios.post("/api/get_cmdb_device_management",this.search);this.userArr=d.data.msg,this.search.total=d.data.total}catch(d){console.error("数据加载失败:",d),this.$message.error("数据加载失败")}finally{this.loading=!1}},async validateAndSubmitAdd(){try{await this.$refs.addFormRef.validate(),await this.submitAdd()}catch{this.$message.error("请完善必填项后再提交")}},async submitAdd(){var d,a;try{const p={...this.formData,usernameby:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/add_cmdb_device_management",p),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(p){console.error("添加失败:",p),this.$message.error(((a=(d=p.response)==null?void 0:d.data)==null?void 0:a.msg)||"添加失败")}},async validateAndSubmitEdit(){try{await this.$refs.editFormRef.validate(),await this.submitEdit()}catch{this.$message.error("请完善必填项后再提交")}},async submitEdit(){var d,a;try{const p={...this.formData,usernameby:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/update_cmdb_device_management",p),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(p){console.error("更新失败:",p),this.$message.error(((a=(d=p.response)==null?void 0:d.data)==null?void 0:a.msg)||"更新失败")}},async submitDelete(){try{await this.$axios.post("/api/del_cmdb_device_management",this.formData),this.$message.success("删除成功"),this.loadData(),this.dialogVisible.delete=!1}catch(d){console.error("删除失败:",d),this.$message.error("删除失败")}},resetSearch(){this.search={management_ip:"",out_of_band_management:null,hostname:null,admin1:null,admin2:null,device_type:null,production_attributes:null,data_center:null,operation_status:null,is_innovative_tech:null,is_monitored:null,online_status:null,year_category:null,serial_number:null,total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},async getDatadict(d,a){try{const p=await this.$axios.post("/api/get_cmdb_data_dictionary",{dict_code:d},{withCredentials:!0});this[a]=p.data.msg}catch(p){console.error("数据加载失败:",p),this.$message.error("数据加载失败")}},handleAdd(){this.dialogVisible.add=!this.dialogVisible.add,this.formData={is_single_point:"否",is_innovative_tech:"否",is_monitored:"否",in_monitoring_list:"否",inspection:"否",managed_addresses:""},this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields(),this.validateManagedAddresses()})},handleEdit(d,a){this.dialogVisible.edit=!0,this.formData.id=a.id,this.formData.management_ip=a.management_ip,this.formData.out_of_band_management=a.out_of_band_management,this.formData.hostname=a.hostname,this.formData.function_purpose=a.function_purpose,this.formData.admin1=a.admin1,this.formData.admin2=a.admin2,this.formData.device_type=a.device_type,this.formData.production_attributes=a.production_attributes,this.formData.data_center=a.data_center,this.formData.operation_status=a.operation_status,this.formData.asset_number=a.asset_number,this.formData.purchase_date=a.purchase_date,this.formData.maintenance_years=a.maintenance_years,this.formData.maintenance_end_date=a.maintenance_end_date,this.formData.serial_number=a.serial_number,this.formData.model=a.model,this.formData.version=a.version,this.formData.cpu_model=a.cpu_model,this.formData.is_innovative_tech=a.is_innovative_tech,this.formData.is_monitored=a.is_monitored,this.formData.monitoring_ip=a.monitoring_ip,this.formData.architecture_mode=a.architecture_mode,this.formData.is_single_point=a.is_single_point,this.formData.managed_addresses=a.managed_addresses,this.formData.remarks=a.remarks,this.formData.year_category=a.year_category,this.formData.in_monitoring_list=a.in_monitoring_list,this.formData.pre_monitoring_verified=a.pre_monitoring_verified,this.formData.inspection=a.inspection,this.$refs.editFormRef&&this.$nextTick(()=>{this.$refs.editFormRef.clearValidate()})},handleDelete(d,a){this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=a.id,this.formData.management_ip=a.management_ip},exportData(){const a=this.$refs.table.columns,p=a.map(u=>u.label),U=this.userArr.map(u=>a.map(x=>u[x.property])),t=[p,...U],_=C.aoa_to_sheet(t),n=C.book_new();C.book_append_sheet(n,_,"Sheet1");const r=K(n,{bookType:"xlsx",type:"array"}),s=new Blob([r],{type:"application/octet-stream"});Q.saveAs(s,"网络设备.xlsx")},handleAddFromDiscovery(){this.dialogVisible.add=!0;const{ip_address:d,hostname:a,open_ports:p}=this.$route.query;this.formData={management_ip:d||"",hostname:a||"",function_purpose:"",admin1:"",admin2:"",device_type:"",production_attributes:"",data_center:"",operation_status:"",is_innovative_tech:"否",is_monitored:"否",remarks:p?`开放端口: ${p}`:"",is_single_point:"否",in_monitoring_list:"否",inspection:"否"},this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields(),this.$message.info("请完善资产信息并提交")}),this.$router.replace({path:this.$route.path})},validateManagedAddresses(){this.$refs.addFormRef&&this.$refs.addFormRef.validateField("managed_addresses"),this.$refs.editFormRef&&this.$refs.editFormRef.validateField("managed_addresses"),this.formData.is_single_point==="是"&&(this.formData.managed_addresses="")}}},X={class:"user-manage"},Z={class:"dialogdiv"},$={class:"dialog-footer"},ee={class:"dialogdiv"},le={class:"dialog-footer"},ae={class:"button-container"},te={class:"action-bar unified-action-bar"},oe={class:"action-bar-left"},re={class:"action-bar-right"},ne={style:{display:"flex","white-space":"nowrap"}},se={class:"pagination"};function ie(d,a,p,U,t,_){const n=f("el-input"),r=f("el-form-item"),s=f("el-option"),u=f("el-select"),x=f("el-date-picker"),w=f("el-form"),V=f("el-button"),P=f("el-dialog"),A=f("el-alert"),c=f("el-col"),q=f("Search"),k=f("el-icon"),Y=f("el-row"),I=f("el-card"),z=f("Plus"),R=f("Download"),i=f("el-table-column"),S=f("el-tag"),M=f("el-table"),E=f("el-pagination"),B=H("loading");return m(),h("div",X,[e(P,{modelValue:t.dialogVisible.add,"onUpdate:modelValue":a[25]||(a[25]=l=>t.dialogVisible.add=l),title:"新增网络设备信息",width:"400","align-center":""},{footer:o(()=>[y("div",$,[e(V,{onClick:a[24]||(a[24]=l=>t.dialogVisible.add=!1)},{default:o(()=>[b("返回")]),_:1}),e(V,{type:"primary",onClick:_.validateAndSubmitAdd},{default:o(()=>[b("确定")]),_:1},8,["onClick"])])]),default:o(()=>[y("div",Z,[e(w,{model:t.formData,rules:t.rules,ref:"addFormRef","label-position":"right","status-icon":""},{default:o(()=>[e(r,{prop:"management_ip",label:"管理IP:"},{default:o(()=>[e(n,{modelValue:t.formData.management_ip,"onUpdate:modelValue":a[0]||(a[0]=l=>t.formData.management_ip=l),style:{width:"240px"},clearable:"",placeholder:"请输入管理IP"},null,8,["modelValue"])]),_:1}),e(r,{prop:"out_of_band_management",label:"带外管理IP:"},{default:o(()=>[e(n,{modelValue:t.formData.out_of_band_management,"onUpdate:modelValue":a[1]||(a[1]=l=>t.formData.out_of_band_management=l),style:{width:"240px"},clearable:"",placeholder:"请输入带外管理IP"},null,8,["modelValue"])]),_:1}),e(r,{prop:"hostname",label:"主机名:"},{default:o(()=>[e(n,{modelValue:t.formData.hostname,"onUpdate:modelValue":a[2]||(a[2]=l=>t.formData.hostname=l),style:{width:"240px"},clearable:"",placeholder:"请输入主机名"},null,8,["modelValue"])]),_:1}),e(r,{prop:"function_purpose",label:"功能用途:"},{default:o(()=>[e(n,{modelValue:t.formData.function_purpose,"onUpdate:modelValue":a[3]||(a[3]=l=>t.formData.function_purpose=l),style:{width:"240px"},clearable:"",placeholder:"请输入功能用途"},null,8,["modelValue"])]),_:1}),e(r,{prop:"admin1",label:"管理员1:"},{default:o(()=>[e(n,{modelValue:t.formData.admin1,"onUpdate:modelValue":a[4]||(a[4]=l=>t.formData.admin1=l),style:{width:"240px"},clearable:"",placeholder:"请输入管理员1"},null,8,["modelValue"])]),_:1}),e(r,{prop:"admin2",label:"管理员2:"},{default:o(()=>[e(n,{modelValue:t.formData.admin2,"onUpdate:modelValue":a[5]||(a[5]=l=>t.formData.admin2=l),style:{width:"240px"},clearable:"",placeholder:"请输入管理员2"},null,8,["modelValue"])]),_:1}),e(r,{prop:"device_type",label:"设备类型:"},{default:o(()=>[e(u,{modelValue:t.formData.device_type,"onUpdate:modelValue":a[6]||(a[6]=l=>t.formData.device_type=l),style:{width:"240px"},placeholder:"请选择设备类型"},{default:o(()=>[(m(!0),h(D,null,v(t.devicetypes,l=>(m(),g(s,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"production_attributes",label:"生产属性:"},{default:o(()=>[e(u,{modelValue:t.formData.production_attributes,"onUpdate:modelValue":a[7]||(a[7]=l=>t.formData.production_attributes=l),style:{width:"240px"},placeholder:"请选择生产属性"},{default:o(()=>[(m(!0),h(D,null,v(t.productionattributes,l=>(m(),g(s,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"data_center",label:"数据中心:"},{default:o(()=>[e(u,{modelValue:t.formData.data_center,"onUpdate:modelValue":a[8]||(a[8]=l=>t.formData.data_center=l),style:{width:"240px"},placeholder:"请选择数据中心"},{default:o(()=>[(m(!0),h(D,null,v(t.datacenters,l=>(m(),g(s,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"operation_status",label:"生命周期:"},{default:o(()=>[e(u,{modelValue:t.formData.operation_status,"onUpdate:modelValue":a[9]||(a[9]=l=>t.formData.operation_status=l),style:{width:"240px"},placeholder:"请选择生命周期"},{default:o(()=>[(m(!0),h(D,null,v(t.operationstatus,l=>(m(),g(s,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"asset_number",label:"资产编号:"},{default:o(()=>[e(n,{modelValue:t.formData.asset_number,"onUpdate:modelValue":a[10]||(a[10]=l=>t.formData.asset_number=l),style:{width:"240px"},clearable:"",placeholder:"请输入资产编号"},null,8,["modelValue"])]),_:1}),e(r,{prop:"purchase_date",label:"购买日期:"},{default:o(()=>[e(x,{modelValue:t.formData.purchase_date,"onUpdate:modelValue":a[11]||(a[11]=l=>t.formData.purchase_date=l),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"240px"},placeholder:"选择日期"},null,8,["modelValue"])]),_:1}),e(r,{prop:"maintenance_years",label:"维保年限:"},{default:o(()=>[e(n,{modelValue:t.formData.maintenance_years,"onUpdate:modelValue":a[12]||(a[12]=l=>t.formData.maintenance_years=l),style:{width:"240px"},clearable:"",placeholder:"请输入维保年限"},null,8,["modelValue"])]),_:1}),e(r,{prop:"serial_number",label:"序列号:"},{default:o(()=>[e(n,{modelValue:t.formData.serial_number,"onUpdate:modelValue":a[13]||(a[13]=l=>t.formData.serial_number=l),style:{width:"240px"},clearable:"",placeholder:"请输入序列号"},null,8,["modelValue"])]),_:1}),e(r,{prop:"model",label:"设备型号:"},{default:o(()=>[e(n,{modelValue:t.formData.model,"onUpdate:modelValue":a[14]||(a[14]=l=>t.formData.model=l),style:{width:"240px"},clearable:"",placeholder:"请输入设备型号"},null,8,["modelValue"])]),_:1}),e(r,{prop:"version",label:"版本:"},{default:o(()=>[e(n,{modelValue:t.formData.version,"onUpdate:modelValue":a[15]||(a[15]=l=>t.formData.version=l),style:{width:"240px"},clearable:"",placeholder:"请输入版本"},null,8,["modelValue"])]),_:1}),e(r,{prop:"cpu_model",label:"CPU型号:"},{default:o(()=>[e(n,{modelValue:t.formData.cpu_model,"onUpdate:modelValue":a[16]||(a[16]=l=>t.formData.cpu_model=l),style:{width:"240px"},clearable:"",placeholder:"请输入CPU型号"},null,8,["modelValue"])]),_:1}),e(r,{prop:"is_innovative_tech",label:"是否信创:"},{default:o(()=>[e(u,{modelValue:t.formData.is_innovative_tech,"onUpdate:modelValue":a[17]||(a[17]=l=>t.formData.is_innovative_tech=l),style:{width:"240px"},placeholder:"请选择是否信创"},{default:o(()=>[e(s,{label:"是",value:"是"}),e(s,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"monitoring_ip",label:"监控IP:"},{default:o(()=>[e(n,{modelValue:t.formData.monitoring_ip,"onUpdate:modelValue":a[18]||(a[18]=l=>t.formData.monitoring_ip=l),style:{width:"240px"},clearable:"",placeholder:"请输入监控IP"},null,8,["modelValue"])]),_:1}),e(r,{prop:"is_single_point",label:"是否单点:"},{default:o(()=>[e(u,{modelValue:t.formData.is_single_point,"onUpdate:modelValue":a[19]||(a[19]=l=>t.formData.is_single_point=l),style:{width:"240px"},placeholder:"请选择是否单点",onChange:_.validateManagedAddresses},{default:o(()=>[e(s,{label:"是",value:"是"}),e(s,{label:"否",value:"否"})]),_:1},8,["modelValue","onChange"])]),_:1}),e(r,{prop:"managed_addresses",label:"互备主机IP:",required:t.formData.is_single_point==="否"},{default:o(()=>[e(n,{modelValue:t.formData.managed_addresses,"onUpdate:modelValue":a[20]||(a[20]=l=>t.formData.managed_addresses=l),style:{width:"240px"},clearable:"",placeholder:"请输入互备主机IP，多个IP用英文逗号分隔"},null,8,["modelValue"])]),_:1},8,["required"]),e(r,{prop:"pre_monitoring_verified",label:"预监控验证:"},{default:o(()=>[e(n,{modelValue:t.formData.pre_monitoring_verified,"onUpdate:modelValue":a[21]||(a[21]=l=>t.formData.pre_monitoring_verified=l),style:{width:"240px"},clearable:"",maxlength:"10","show-word-limit":"",placeholder:"请输入预监控验证"},null,8,["modelValue"])]),_:1}),e(r,{prop:"inspection",label:"检查:"},{default:o(()=>[e(u,{modelValue:t.formData.inspection,"onUpdate:modelValue":a[22]||(a[22]=l=>t.formData.inspection=l),style:{width:"240px"},placeholder:"请选择是否检查"},{default:o(()=>[e(s,{label:"是",value:"是"}),e(s,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"remarks",label:"备注:"},{default:o(()=>[e(n,{modelValue:t.formData.remarks,"onUpdate:modelValue":a[23]||(a[23]=l=>t.formData.remarks=l),style:{width:"240px"},clearable:"",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),e(P,{modelValue:t.dialogVisible.edit,"onUpdate:modelValue":a[51]||(a[51]=l=>t.dialogVisible.edit=l),title:"编辑网络设备信息",width:"400","align-center":""},{footer:o(()=>[y("div",le,[e(V,{onClick:a[50]||(a[50]=l=>t.dialogVisible.edit=!1)},{default:o(()=>[b("取消")]),_:1}),e(V,{type:"primary",onClick:_.validateAndSubmitEdit},{default:o(()=>[b("更新")]),_:1},8,["onClick"])])]),default:o(()=>[y("div",ee,[e(w,{model:t.formData,rules:t.rules,ref:"editFormRef","label-position":"right","status-icon":""},{default:o(()=>[e(r,{prop:"management_ip",label:"管理IP:"},{default:o(()=>[e(n,{modelValue:t.formData.management_ip,"onUpdate:modelValue":a[26]||(a[26]=l=>t.formData.management_ip=l),style:{width:"240px"},clearable:"",placeholder:"请输入管理IP"},null,8,["modelValue"])]),_:1}),e(r,{prop:"out_of_band_management",label:"带外管理IP:"},{default:o(()=>[e(n,{modelValue:t.formData.out_of_band_management,"onUpdate:modelValue":a[27]||(a[27]=l=>t.formData.out_of_band_management=l),style:{width:"240px"},clearable:"",placeholder:"请输入带外管理IP"},null,8,["modelValue"])]),_:1}),e(r,{prop:"hostname",label:"主机名:"},{default:o(()=>[e(n,{modelValue:t.formData.hostname,"onUpdate:modelValue":a[28]||(a[28]=l=>t.formData.hostname=l),style:{width:"240px"},clearable:"",placeholder:"请输入主机名"},null,8,["modelValue"])]),_:1}),e(r,{prop:"function_purpose",label:"功能用途:"},{default:o(()=>[e(n,{modelValue:t.formData.function_purpose,"onUpdate:modelValue":a[29]||(a[29]=l=>t.formData.function_purpose=l),style:{width:"240px"},clearable:"",placeholder:"请输入功能用途"},null,8,["modelValue"])]),_:1}),e(r,{prop:"admin1",label:"管理员1:"},{default:o(()=>[e(n,{modelValue:t.formData.admin1,"onUpdate:modelValue":a[30]||(a[30]=l=>t.formData.admin1=l),style:{width:"240px"},clearable:"",placeholder:"请输入管理员1"},null,8,["modelValue"])]),_:1}),e(r,{prop:"admin2",label:"管理员2:"},{default:o(()=>[e(n,{modelValue:t.formData.admin2,"onUpdate:modelValue":a[31]||(a[31]=l=>t.formData.admin2=l),style:{width:"240px"},clearable:"",placeholder:"请输入管理员2"},null,8,["modelValue"])]),_:1}),e(r,{prop:"device_type",label:"设备类型:"},{default:o(()=>[e(u,{modelValue:t.formData.device_type,"onUpdate:modelValue":a[32]||(a[32]=l=>t.formData.device_type=l),style:{width:"240px"},placeholder:"请选择设备类型"},{default:o(()=>[(m(!0),h(D,null,v(t.devicetypes,l=>(m(),g(s,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"production_attributes",label:"生产属性:"},{default:o(()=>[e(u,{modelValue:t.formData.production_attributes,"onUpdate:modelValue":a[33]||(a[33]=l=>t.formData.production_attributes=l),style:{width:"240px"},placeholder:"请选择生产属性"},{default:o(()=>[(m(!0),h(D,null,v(t.productionattributes,l=>(m(),g(s,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"data_center",label:"数据中心:"},{default:o(()=>[e(u,{modelValue:t.formData.data_center,"onUpdate:modelValue":a[34]||(a[34]=l=>t.formData.data_center=l),style:{width:"240px"},placeholder:"请选择数据中心"},{default:o(()=>[(m(!0),h(D,null,v(t.datacenters,l=>(m(),g(s,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"operation_status",label:"生命周期:"},{default:o(()=>[e(u,{modelValue:t.formData.operation_status,"onUpdate:modelValue":a[35]||(a[35]=l=>t.formData.operation_status=l),style:{width:"240px"},placeholder:"请选择生命周期"},{default:o(()=>[(m(!0),h(D,null,v(t.operationstatus,l=>(m(),g(s,{key:l.dict_code,label:l.dict_name,value:l.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"asset_number",label:"资产编号:"},{default:o(()=>[e(n,{modelValue:t.formData.asset_number,"onUpdate:modelValue":a[36]||(a[36]=l=>t.formData.asset_number=l),style:{width:"240px"},clearable:"",placeholder:"请输入资产编号"},null,8,["modelValue"])]),_:1}),e(r,{prop:"purchase_date",label:"购买日期:"},{default:o(()=>[e(x,{modelValue:t.formData.purchase_date,"onUpdate:modelValue":a[37]||(a[37]=l=>t.formData.purchase_date=l),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"240px"},placeholder:"选择日期"},null,8,["modelValue"])]),_:1}),e(r,{prop:"maintenance_years",label:"维保年限:"},{default:o(()=>[e(n,{modelValue:t.formData.maintenance_years,"onUpdate:modelValue":a[38]||(a[38]=l=>t.formData.maintenance_years=l),style:{width:"240px"},clearable:"",placeholder:"请输入维保年限"},null,8,["modelValue"])]),_:1}),e(r,{prop:"serial_number",label:"序列号:"},{default:o(()=>[e(n,{modelValue:t.formData.serial_number,"onUpdate:modelValue":a[39]||(a[39]=l=>t.formData.serial_number=l),style:{width:"240px"},clearable:"",placeholder:"请输入序列号"},null,8,["modelValue"])]),_:1}),e(r,{prop:"model",label:"设备型号:"},{default:o(()=>[e(n,{modelValue:t.formData.model,"onUpdate:modelValue":a[40]||(a[40]=l=>t.formData.model=l),style:{width:"240px"},clearable:"",placeholder:"请输入设备型号"},null,8,["modelValue"])]),_:1}),e(r,{prop:"version",label:"版本:"},{default:o(()=>[e(n,{modelValue:t.formData.version,"onUpdate:modelValue":a[41]||(a[41]=l=>t.formData.version=l),style:{width:"240px"},clearable:"",placeholder:"请输入版本"},null,8,["modelValue"])]),_:1}),e(r,{prop:"cpu_model",label:"CPU型号:"},{default:o(()=>[e(n,{modelValue:t.formData.cpu_model,"onUpdate:modelValue":a[42]||(a[42]=l=>t.formData.cpu_model=l),style:{width:"240px"},clearable:"",placeholder:"请输入CPU型号"},null,8,["modelValue"])]),_:1}),e(r,{prop:"is_innovative_tech",label:"是否信创:"},{default:o(()=>[e(u,{modelValue:t.formData.is_innovative_tech,"onUpdate:modelValue":a[43]||(a[43]=l=>t.formData.is_innovative_tech=l),style:{width:"240px"},placeholder:"请选择是否信创"},{default:o(()=>[e(s,{label:"是",value:"是"}),e(s,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"monitoring_ip",label:"监控IP:"},{default:o(()=>[e(n,{modelValue:t.formData.monitoring_ip,"onUpdate:modelValue":a[44]||(a[44]=l=>t.formData.monitoring_ip=l),style:{width:"240px"},clearable:"",placeholder:"请输入监控IP"},null,8,["modelValue"])]),_:1}),e(r,{prop:"is_single_point",label:"是否单点:"},{default:o(()=>[e(u,{modelValue:t.formData.is_single_point,"onUpdate:modelValue":a[45]||(a[45]=l=>t.formData.is_single_point=l),style:{width:"240px"},placeholder:"请选择是否单点",onChange:_.validateManagedAddresses},{default:o(()=>[e(s,{label:"是",value:"是"}),e(s,{label:"否",value:"否"})]),_:1},8,["modelValue","onChange"])]),_:1}),e(r,{prop:"managed_addresses",label:"互备主机IP:",required:t.formData.is_single_point==="否"},{default:o(()=>[e(n,{modelValue:t.formData.managed_addresses,"onUpdate:modelValue":a[46]||(a[46]=l=>t.formData.managed_addresses=l),style:{width:"240px"},clearable:"",placeholder:"请输入互备主机IP，多个IP用英文逗号分隔"},null,8,["modelValue"])]),_:1},8,["required"]),e(r,{prop:"pre_monitoring_verified",label:"预监控验证:"},{default:o(()=>[e(n,{modelValue:t.formData.pre_monitoring_verified,"onUpdate:modelValue":a[47]||(a[47]=l=>t.formData.pre_monitoring_verified=l),style:{width:"240px"},clearable:"",maxlength:"10","show-word-limit":"",placeholder:"请输入预监控验证"},null,8,["modelValue"])]),_:1}),e(r,{prop:"inspection",label:"检查:"},{default:o(()=>[e(u,{modelValue:t.formData.inspection,"onUpdate:modelValue":a[48]||(a[48]=l=>t.formData.inspection=l),style:{width:"240px"},placeholder:"请选择是否检查"},{default:o(()=>[e(s,{label:"是",value:"是"}),e(s,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),e(r,{prop:"remarks",label:"备注:"},{default:o(()=>[e(n,{modelValue:t.formData.remarks,"onUpdate:modelValue":a[49]||(a[49]=l=>t.formData.remarks=l),style:{width:"240px"},clearable:"",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),e(P,{modelValue:t.dialogVisible.delete,"onUpdate:modelValue":a[53]||(a[53]=l=>t.dialogVisible.delete=l),title:"删除管理IP",width:"500","align-center":""},{footer:o(()=>[y("div",null,[e(V,{onClick:a[52]||(a[52]=l=>t.dialogVisible.delete=!1)},{default:o(()=>[b("取消")]),_:1}),e(V,{type:"danger",onClick:_.submitDelete},{default:o(()=>[b("确认删除")]),_:1},8,["onClick"])])]),default:o(()=>[e(A,{type:"warning",title:`确定要删除 IP 为 ${t.formData.management_ip} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),e(I,{class:"search-card"},{default:o(()=>[e(w,{inline:!0},{default:o(()=>[e(Y,{gutter:10},{default:o(()=>[e(c,{span:6},{default:o(()=>[e(r,{label:"管理IP"},{default:o(()=>[e(n,{modelValue:t.search.management_ip,"onUpdate:modelValue":a[54]||(a[54]=l=>t.search.management_ip=l),placeholder:"请输入管理IP",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(c,{span:6},{default:o(()=>[e(r,{label:"带外管理IP"},{default:o(()=>[e(n,{modelValue:t.search.out_of_band_management,"onUpdate:modelValue":a[55]||(a[55]=l=>t.search.out_of_band_management=l),placeholder:"请输入带外管理IP",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(c,{span:6},{default:o(()=>[e(r,{label:"主机名"},{default:o(()=>[e(n,{modelValue:t.search.hostname,"onUpdate:modelValue":a[56]||(a[56]=l=>t.search.hostname=l),placeholder:"请输入主机名",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(c,{span:6},{default:o(()=>[e(r,{label:"设备类型"},{default:o(()=>[e(u,{modelValue:t.search.device_type,"onUpdate:modelValue":a[57]||(a[57]=l=>t.search.device_type=l),placeholder:"请选择设备类型",class:"form-control"},{default:o(()=>[e(s,{label:"所有",value:""}),(m(!0),h(D,null,v(t.devicetypes,l=>(m(),g(s,{key:l.dict_code,label:l.dict_name,value:l.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(c,{span:6},{default:o(()=>[e(r,{label:"管理员1"},{default:o(()=>[e(n,{modelValue:t.search.admin1,"onUpdate:modelValue":a[58]||(a[58]=l=>t.search.admin1=l),placeholder:"请输入管理员1",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(c,{span:6},{default:o(()=>[e(r,{label:"管理员2"},{default:o(()=>[e(n,{modelValue:t.search.admin2,"onUpdate:modelValue":a[59]||(a[59]=l=>t.search.admin2=l),placeholder:"请输入管理员2",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(c,{span:6},{default:o(()=>[e(r,{label:"生产属性"},{default:o(()=>[e(u,{modelValue:t.search.production_attributes,"onUpdate:modelValue":a[60]||(a[60]=l=>t.search.production_attributes=l),placeholder:"请选择生产属性",class:"form-control"},{default:o(()=>[e(s,{label:"所有",value:""}),(m(!0),h(D,null,v(t.productionattributes,l=>(m(),g(s,{key:l.dict_code,label:l.dict_name,value:l.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(c,{span:6},{default:o(()=>[e(r,{label:"所属机房"},{default:o(()=>[e(u,{modelValue:t.search.data_center,"onUpdate:modelValue":a[61]||(a[61]=l=>t.search.data_center=l),placeholder:"请选择所属机房",class:"form-control"},{default:o(()=>[e(s,{label:"所有",value:""}),(m(!0),h(D,null,v(t.datacenters,l=>(m(),g(s,{key:l.dict_code,label:l.dict_name,value:l.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(c,{span:6},{default:o(()=>[e(r,{label:"序列号"},{default:o(()=>[e(n,{modelValue:t.search.serial_number,"onUpdate:modelValue":a[62]||(a[62]=l=>t.search.serial_number=l),placeholder:"请输入序列号",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(c,{span:6},{default:o(()=>[e(r,{label:"生命周期"},{default:o(()=>[e(u,{modelValue:t.search.operation_status,"onUpdate:modelValue":a[63]||(a[63]=l=>t.search.operation_status=l),placeholder:"请选择生命周期",class:"form-control"},{default:o(()=>[e(s,{label:"所有",value:""}),(m(!0),h(D,null,v(t.operationstatus,l=>(m(),g(s,{key:l.dict_code,label:l.dict_name,value:l.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(c,{span:6},{default:o(()=>[e(r,{label:"是否信创"},{default:o(()=>[e(u,{modelValue:t.search.is_innovative_tech,"onUpdate:modelValue":a[64]||(a[64]=l=>t.search.is_innovative_tech=l),placeholder:"请选择是否信创",class:"form-control"},{default:o(()=>[e(s,{label:"所有",value:""}),e(s,{label:"是",value:"是"}),e(s,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(c,{span:6},{default:o(()=>[e(r,{label:"是否监控"},{default:o(()=>[e(u,{modelValue:t.search.is_monitored,"onUpdate:modelValue":a[65]||(a[65]=l=>t.search.is_monitored=l),placeholder:"请选择是否监控",class:"form-control"},{default:o(()=>[e(s,{label:"所有",value:""}),e(s,{label:"是",value:"是"}),e(s,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(c,{span:6},{default:o(()=>[e(r,{label:"是否在线"},{default:o(()=>[e(u,{modelValue:t.search.online_status,"onUpdate:modelValue":a[66]||(a[66]=l=>t.search.online_status=l),placeholder:"请选择是否在线",class:"form-control"},{default:o(()=>[e(s,{label:"所有",value:""}),e(s,{label:"在线",value:"在线"}),e(s,{label:"离线",value:"离线"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(c,{span:18,class:"search-buttons-col"},{default:o(()=>[e(r,{label:" ",class:"form-item-with-label search-buttons"},{default:o(()=>[y("div",ae,[e(V,{type:"primary",onClick:_.loadData},{default:o(()=>[e(k,null,{default:o(()=>[e(q)]),_:1}),b("查询 ")]),_:1},8,["onClick"]),e(V,{onClick:_.resetSearch},{default:o(()=>[b("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),y("div",te,[y("div",oe,[e(V,{type:"success",disabled:!t.hasInsertPermission,onClick:_.handleAdd},{default:o(()=>[e(k,null,{default:o(()=>[e(z)]),_:1}),b("新增资产 ")]),_:1},8,["disabled","onClick"])]),y("div",re,[e(V,{type:"info",onClick:_.exportData},{default:o(()=>[e(k,null,{default:o(()=>[e(R)]),_:1}),b(" 导出数据 ")]),_:1},8,["onClick"])])]),e(I,{class:"table-card"},{default:o(()=>[G((m(),g(M,{data:t.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:_.handleSortChange},{default:o(()=>[J("",!0),e(i,{prop:"management_ip",label:"管理IP",sortable:""}),e(i,{prop:"out_of_band_management",label:"带外管理IP",sortable:""}),e(i,{prop:"hostname",label:"主机名",sortable:""}),e(i,{prop:"function_purpose",label:"功能用途",sortable:""}),e(i,{prop:"admin1",label:"管理员1",sortable:""}),e(i,{prop:"admin2",label:"管理员2",sortable:""}),e(i,{prop:"device_type",label:"设备类型",sortable:""}),e(i,{prop:"production_attributes",label:"生产属性",sortable:""}),e(i,{prop:"data_center",label:"所属机房",sortable:""}),e(i,{prop:"operation_status",label:"生命周期",sortable:""}),e(i,{prop:"asset_number",label:"财务资产编号",sortable:""}),e(i,{prop:"purchase_date",label:"采购时间",sortable:""}),e(i,{prop:"maintenance_years",label:"维保年限",sortable:""}),e(i,{prop:"maintenance_end_date",label:"维保截止日期",sortable:""}),e(i,{prop:"serial_number",label:"序列号",sortable:""}),e(i,{prop:"model",label:"设备型号",sortable:""}),e(i,{prop:"version",label:"版本",sortable:""}),e(i,{prop:"cpu_model",label:"CPU型号",sortable:""}),e(i,{prop:"is_innovative_tech",label:"是否信创",sortable:""}),e(i,{prop:"is_monitored",label:"是否监控",sortable:""},{default:o(l=>[e(S,{type:l.row.is_monitored==="是"?"success":"info"},{default:o(()=>[b(F(l.row.is_monitored),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"online_status",label:"是否在线",sortable:""},{default:o(l=>[e(S,{type:l.row.online_status==="在线"?"success":"danger"},{default:o(()=>[b(F(l.row.online_status),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"monitoring_ip",label:"配置的监控IP",sortable:""}),e(i,{prop:"is_single_point",label:"是否单点",sortable:""}),e(i,{prop:"managed_addresses",label:"互备主机IP",sortable:""}),e(i,{prop:"pre_monitoring_verified",label:"监控前期是否验证",sortable:""}),e(i,{prop:"inspection",label:"抽查",sortable:""}),e(i,{prop:"remarks",label:"备注",sortable:""}),e(i,{prop:"created_at",label:"创建时间",sortable:""}),e(i,{prop:"created_by",label:"创建人",sortable:""}),e(i,{prop:"updated_at",label:"更新时间",sortable:""}),e(i,{prop:"updated_by",label:"更新人",sortable:""}),e(i,{label:"操作",fixed:"right"},{default:o(l=>[y("div",ne,[e(V,{size:"small",type:"warning",disabled:!t.hasUpdatePermission,onClick:T=>_.handleEdit(l.$index,l.row)},{default:o(()=>[b("编辑")]),_:2},1032,["disabled","onClick"]),e(V,{size:"small",type:"danger",disabled:!t.hasDeletePermission,onClick:T=>_.handleDelete(l.$index,l.row)},{default:o(()=>[b("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[B,t.loading]]),y("div",se,[e(E,{background:"","current-page":t.search.currentPage,"page-size":t.search.pageSize,total:t.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:_.handlePageSizeChange,onCurrentChange:_.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const me=N(W,[["render",ie],["__scopeId","data-v-c57e9f9c"]]);export{me as default};
