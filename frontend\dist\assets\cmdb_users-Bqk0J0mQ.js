import{_ as T,z as j,v as q,A as L,c as y,a,w as r,f as o,b as i,F as k,l as S,h as c,B as M,C as G,m as w,x as I,E as f,p as H,q as J,e as _}from"./index-C0WPPkX4.js";import{u as C,w as K,F as Q}from"./FileSaver.min-BAET_C7X.js";const W={components:{Plus:L,Search:q,Download:j},data(){var t,e,h;return{userArr:[],loading:!1,showRoleColumn:!1,hasDeletePermission:(t=localStorage.getItem("role_code"))==null?void 0:t.includes("D"),hasUpdatePermission:(e=localStorage.getItem("role_code"))==null?void 0:e.includes("U"),hasInsertPermission:(h=localStorage.getItem("role_code"))==null?void 0:h.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{username:"",total:0,pageSize:10,currentPage:1,loginUsername:localStorage.getItem("loginUsername"),sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,username:"",password:"",role_code:"",created_at:null,role_code_name:"",real_name:"",phone:"",email:"",wechat_id:""},selectedRoles:[],roleOptions:[{value:"I",label:"I:增"},{value:"D",label:"D:删"},{value:"U",label:"U:改"}]}},mounted(){this.loadData()},methods:{handlePageChange(t){this.search.currentPage=t,this.loadData()},handlePageSizeChange(t){this.search.pageSize=parseInt(t),this.search.currentPage=1,this.loadData()},updateRoleCode(t){this.formData.role_code=t.join(",")},handleSortChange({column:t,prop:e,order:h}){this.search.sortProp=e,this.search.sortOrder=h==="ascending"?"asc":"desc",this.loadData()},async loadData(){try{this.loading=!0;const t=await this.$axios.post("/api/get_cmdb_users",this.search);this.userArr=t.data.msg,this.search.total=t.data.total}catch(t){console.error("数据加载失败:",t),this.$message.error("数据加载失败")}finally{this.loading=!1}},async submitAdd(){if(this.formData.username=this.formData.username.trim(),!this.formData.username||!this.formData.password){alert("请输入用户名和密码！");return}this.formData.usernameby=this.search.loginUsername;try{await this.$axios.post("/api/add_cmdb_users",this.formData),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(t){console.error("添加失败:",t),t.response&&t.response.data&&t.response.data.msg?this.$message.error(`添加失败: ${t.response.data.msg}`):this.$message.error("添加失败")}},async submitEdit(){this.formData.usernameby=this.search.loginUsername;const t={...this.formData};this.search.loginUsername!=="admin"&&delete t.role_code;try{const e=await this.$axios.post("/api/update_cmdb_users",t);e.data.code===0?(this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()):this.$message.error(`更新失败: ${e.data.msg}`)}catch(e){console.error("更新失败:",e),e.response&&e.response.data&&e.response.data.msg?this.$message.error(`更新失败: ${e.response.data.msg}`):this.$message.error("更新失败")}},async submitDelete(t){this.formData.usernameby=this.search.loginUsername;try{const e=await this.$axios.post("/api/del_cmdb_users",this.formData);e.data.code===0?f.success("删除成功"):f.error(`删除失败: ${e.data.msg}`),this.loadData(),this.dialogVisible.delete=!1}catch(e){console.error("删除失败:",e),e.response&&e.response.data&&e.response.data.msg?this.$message.error(`删除失败: ${e.response.data.msg}`):this.$message.error("删除失败")}},handleAdd(t,e){if(this.search.loginUsername!=="admin"){f.error("只有管理员可以添加用户");return}this.dialogVisible.add=!this.dialogVisible.add,this.formData={usernameby:this.search.loginUsername},this.selectedRoles=[]},handleEdit(t,e){if(this.search.loginUsername!=="admin"&&e.username!==this.search.loginUsername){f.error("您只能编辑自己的信息");return}this.dialogVisible.edit=!0,this.formData.id=e.id,this.formData.username=e.username,this.formData.real_name=e.real_name||"",this.formData.phone=e.phone||"",this.formData.email=e.email||"",this.formData.wechat_id=e.wechat_id||"",this.formData.password="",this.formData.role_code=e.role_code,this.selectedRoles=e.role_code?e.role_code.replace(/^,|,$/g,"").split(","):[],this.formData.usernameby=this.search.loginUsername},handleDelete(t,e){if(this.search.loginUsername!=="admin"){f.error("只有管理员可以删除用户");return}this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=e.id,this.formData.username=e.username,this.formData.usernameby=this.search.loginUsername},exportData(){const e=this.$refs.table.columns,h=e.map(m=>m.label),U=this.userArr.map(m=>e.map(b=>m[b.property])),l=[h,...U],d=C.aoa_to_sheet(l),n=C.book_new();C.book_append_sheet(n,d,"Sheet1");const g=K(n,{bookType:"xlsx",type:"array"}),D=new Blob([g],{type:"application/octet-stream"});Q.saveAs(D,"用户管理.xlsx")}}},u=t=>(H("data-v-1337ac13"),t=t(),J(),t),X={class:"user-manage"},Y={class:"dialogdiv"},Z=u(()=>o("span",{class:"label"},"用户名:",-1)),$=u(()=>o("span",{class:"label"},"用户姓名:",-1)),ee=u(()=>o("span",{class:"label"},"联系电话:",-1)),le=u(()=>o("span",{class:"label"},"电子邮箱:",-1)),ae=u(()=>o("span",{class:"label"},"企业微信ID:",-1)),se=u(()=>o("span",{class:"label"},"初始密码:",-1)),oe=u(()=>o("span",{class:"label"},"权限代码:",-1)),te={class:"dialog-footer"},re={class:"dialogdiv"},ne=u(()=>o("span",{class:"label"},"用户名:",-1)),ie=u(()=>o("span",{class:"label"},"用户姓名:",-1)),de=u(()=>o("span",{class:"label"},"联系电话:",-1)),me=u(()=>o("span",{class:"label"},"电子邮箱:",-1)),ue=u(()=>o("span",{class:"label"},"企业微信ID:",-1)),pe=u(()=>o("span",{class:"label"},"密码:",-1)),ce=u(()=>o("span",{class:"label"},"权限代码:",-1)),he={class:"dialog-footer"},_e={style:{display:"flex","white-space":"nowrap"}},be={class:"pagination"};function fe(t,e,h,U,l,d){const n=i("el-input"),g=i("el-option"),D=i("el-select"),m=i("el-button"),b=i("el-dialog"),P=i("el-alert"),x=i("el-form-item"),z=i("Search"),V=i("el-icon"),R=i("Plus"),A=i("Download"),B=i("el-form"),v=i("el-card"),p=i("el-table-column"),E=i("el-table"),O=i("el-pagination"),F=G("loading");return _(),y("div",X,[a(b,{modelValue:l.dialogVisible.add,"onUpdate:modelValue":e[8]||(e[8]=s=>l.dialogVisible.add=s),title:"新增用户",width:"400","align-center":""},{footer:r(()=>[o("div",te,[a(m,{onClick:e[7]||(e[7]=s=>l.dialogVisible.add=!1)},{default:r(()=>[c("返回")]),_:1}),a(m,{type:"primary",onClick:d.submitAdd},{default:r(()=>[c("确定")]),_:1},8,["onClick"])])]),default:r(()=>[o("div",Y,[o("p",null,[Z,a(n,{modelValue:l.formData.username,"onUpdate:modelValue":e[0]||(e[0]=s=>l.formData.username=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[$,a(n,{modelValue:l.formData.real_name,"onUpdate:modelValue":e[1]||(e[1]=s=>l.formData.real_name=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[ee,a(n,{modelValue:l.formData.phone,"onUpdate:modelValue":e[2]||(e[2]=s=>l.formData.phone=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[le,a(n,{modelValue:l.formData.email,"onUpdate:modelValue":e[3]||(e[3]=s=>l.formData.email=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[ae,a(n,{modelValue:l.formData.wechat_id,"onUpdate:modelValue":e[4]||(e[4]=s=>l.formData.wechat_id=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[se,a(n,{modelValue:l.formData.password,"onUpdate:modelValue":e[5]||(e[5]=s=>l.formData.password=s),style:{width:"240px"},clearable:"",type:"password"},null,8,["modelValue"])]),o("p",null,[oe,a(D,{modelValue:l.selectedRoles,"onUpdate:modelValue":e[6]||(e[6]=s=>l.selectedRoles=s),multiple:"",style:{width:"240px"},placeholder:"请选择角色",onChange:d.updateRoleCode},{default:r(()=>[(_(!0),y(k,null,S(l.roleOptions,s=>(_(),w(g,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])])])]),_:1},8,["modelValue"]),a(b,{modelValue:l.dialogVisible.edit,"onUpdate:modelValue":e[17]||(e[17]=s=>l.dialogVisible.edit=s),title:"更新用户信息",width:"400","align-center":""},{footer:r(()=>[o("div",he,[a(m,{onClick:e[16]||(e[16]=s=>l.dialogVisible.edit=!1)},{default:r(()=>[c("取消")]),_:1}),a(m,{type:"primary",onClick:d.submitEdit},{default:r(()=>[c("更新")]),_:1},8,["onClick"])])]),default:r(()=>[o("div",re,[o("p",null,[ne,a(n,{modelValue:l.formData.username,"onUpdate:modelValue":e[9]||(e[9]=s=>l.formData.username=s),style:{width:"240px"},clearable:"",disabled:""},null,8,["modelValue"])]),o("p",null,[ie,a(n,{modelValue:l.formData.real_name,"onUpdate:modelValue":e[10]||(e[10]=s=>l.formData.real_name=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[de,a(n,{modelValue:l.formData.phone,"onUpdate:modelValue":e[11]||(e[11]=s=>l.formData.phone=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[me,a(n,{modelValue:l.formData.email,"onUpdate:modelValue":e[12]||(e[12]=s=>l.formData.email=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[ue,a(n,{modelValue:l.formData.wechat_id,"onUpdate:modelValue":e[13]||(e[13]=s=>l.formData.wechat_id=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[pe,a(n,{modelValue:l.formData.password,"onUpdate:modelValue":e[14]||(e[14]=s=>l.formData.password=s),style:{width:"240px"},clearable:"",placeholder:"为空不会更新密码",type:"password"},null,8,["modelValue"])]),o("p",null,[ce,a(D,{modelValue:l.selectedRoles,"onUpdate:modelValue":e[15]||(e[15]=s=>l.selectedRoles=s),multiple:"",style:{width:"240px"},placeholder:"请选择角色",disabled:l.search.loginUsername!=="admin",onChange:d.updateRoleCode},{default:r(()=>[(_(!0),y(k,null,S(l.roleOptions,s=>(_(),w(g,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled","onChange"])])])]),_:1},8,["modelValue"]),a(b,{modelValue:l.dialogVisible.delete,"onUpdate:modelValue":e[19]||(e[19]=s=>l.dialogVisible.delete=s),title:"删除用户",width:"500","align-center":""},{footer:r(()=>[o("div",null,[a(m,{onClick:e[18]||(e[18]=s=>l.dialogVisible.delete=!1)},{default:r(()=>[c("取消")]),_:1}),a(m,{type:"danger",onClick:d.submitDelete},{default:r(()=>[c("确认删除")]),_:1},8,["onClick"])])]),default:r(()=>[a(P,{type:"warning",title:`确定要删除 IP 为 ${l.formData.username} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),a(v,{class:"search-card"},{default:r(()=>[a(B,{inline:!0},{default:r(()=>[a(x,{label:"用户名："},{default:r(()=>[a(n,{modelValue:l.search.username,"onUpdate:modelValue":e[20]||(e[20]=s=>l.search.username=s),placeholder:"请输入用户名",clearable:""},null,8,["modelValue"])]),_:1}),a(x,null,{default:r(()=>[a(m,{type:"primary",onClick:d.loadData},{default:r(()=>[a(V,null,{default:r(()=>[a(z)]),_:1}),c("查询 ")]),_:1},8,["onClick"]),a(m,{disabled:l.search.loginUsername!=="admin",type:"success",onClick:d.handleAdd},{default:r(()=>[a(V,null,{default:r(()=>[a(R)]),_:1}),c("新增 ")]),_:1},8,["disabled","onClick"]),a(m,{type:"info",onClick:d.exportData},{default:r(()=>[a(V,null,{default:r(()=>[a(A)]),_:1}),c("导出 ")]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1}),a(v,{class:"table-card"},{default:r(()=>[M((_(),w(E,{data:l.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:d.handleSortChange},{default:r(()=>[I("",!0),a(p,{prop:"username",label:"用户名",sortable:""}),a(p,{prop:"real_name",label:"用户姓名",sortable:""}),a(p,{prop:"phone",label:"联系电话",sortable:""}),a(p,{prop:"email",label:"电子邮箱",sortable:""}),a(p,{prop:"wechat_id",label:"企业微信ID",sortable:""}),a(p,{prop:"role_code_name",label:"权限",sortable:""}),I("",!0),a(p,{prop:"created_at",label:"创建时间",sortable:""}),a(p,{prop:"created_by",label:"创建人",sortable:""}),a(p,{prop:"updated_at",label:"更新时间",sortable:""}),a(p,{prop:"updated_by",label:"更新人",sortable:""}),a(p,{label:"操作",fixed:"right"},{default:r(s=>[o("div",_e,[a(m,{size:"small",type:"warning",disabled:l.search.loginUsername!=="admin"&&s.row.username!==l.search.loginUsername,onClick:N=>d.handleEdit(s.$index,s.row)},{default:r(()=>[c("编辑")]),_:2},1032,["disabled","onClick"]),a(m,{size:"small",type:"danger",disabled:l.search.loginUsername!=="admin",onClick:N=>d.handleDelete(s.$index,s.row)},{default:r(()=>[c("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[F,l.loading]]),o("div",be,[a(O,{background:"","current-page":l.search.currentPage,"page-size":l.search.pageSize,total:l.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:d.handlePageSizeChange,onCurrentChange:d.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const Ve=T(W,[["render",fe],["__scopeId","data-v-1337ac13"]]);export{Ve as default};
