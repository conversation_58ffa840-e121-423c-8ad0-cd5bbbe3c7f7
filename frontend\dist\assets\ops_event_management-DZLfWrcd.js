import{_ as M,v as U,A as Y,c as x,a as e,w as a,B,f as h,b as n,h as p,C as N,m as A,t as w,p as E,q as R,e as V}from"./index-Bo9zKuAo.js";const j={name:"EventManagement",components:{Plus:Y,Search:U},data(){return{loading:!1,search:{eventId:"",eventType:"",status:"",reporter:"",priority:"",dateRange:[],currentPage:1,pageSize:10},tableData:[{id:1,eventId:"INC-2025-001",title:"核心交换机端口故障",eventType:"故障",status:"已解决",priority:"高",reporter:"张三",assignee:"李四",reportTime:"2025-05-15 08:30",resolveTime:"2025-05-15 10:45",description:"数据中心核心交换机G1/0/24端口无法正常工作，导致部分服务器无法访问。"},{id:2,eventId:"INC-2025-002",title:"应用服务器CPU使用率过高",eventType:"告警",status:"处理中",priority:"中",reporter:"系统监控",assignee:"王五",reportTime:"2025-05-16 14:20",resolveTime:null,description:"APP01服务器CPU使用率持续超过90%，可能影响应用性能。"}],total:2}},methods:{loadData(){this.loading=!0,setTimeout(()=>{this.loading=!1},500)},resetSearch(){this.search={eventId:"",eventType:"",status:"",reporter:"",priority:"",dateRange:[],currentPage:1,pageSize:10},this.loadData()},handleSizeChange(t){this.search.pageSize=t,this.loadData()},handleCurrentChange(t){this.search.currentPage=t,this.loadData()},handleAdd(){this.$message.info("新增事件功能待实现")},handleView(t){this.$message.info(`查看事件: ${t.eventId}`)},handleEdit(t){this.$message.info(`编辑事件: ${t.eventId}`)},handleDelete(t){this.$message.info(`删除事件: ${t.eventId}`)},getStatusType(t){return{待处理:"info",处理中:"warning",已解决:"success",已关闭:"info",已升级:"danger"}[t]||"info"},getPriorityType(t){return{紧急:"danger",高:"danger",中:"warning",低:"info"}[t]||"info"}},mounted(){this.loadData()}},q=t=>(E("data-v-6a4147d6"),t=t(),R(),t),G={class:"event-management"},F={class:"card-header"},H=q(()=>h("h2",null,"事件管理",-1)),J={class:"header-actions"},K={class:"button-container"},L={class:"pagination"};function O(t,s,Q,W,o,d){const S=n("Plus"),f=n("el-icon"),_=n("el-button"),g=n("el-input"),u=n("el-form-item"),c=n("el-col"),r=n("el-option"),m=n("el-select"),v=n("el-row"),T=n("el-date-picker"),k=n("Search"),D=n("el-form"),b=n("el-card"),i=n("el-table-column"),y=n("el-tag"),I=n("el-table"),z=n("el-pagination"),P=N("loading");return V(),x("div",G,[e(b,{class:"main-card"},{header:a(()=>[h("div",F,[H,h("div",J,[e(_,{type:"primary",onClick:d.handleAdd},{default:a(()=>[e(f,null,{default:a(()=>[e(S)]),_:1}),p("新增事件 ")]),_:1},8,["onClick"])])])]),default:a(()=>[e(b,{class:"search-card"},{default:a(()=>[e(D,{inline:!0,model:o.search,class:"search-form"},{default:a(()=>[e(v,{gutter:20},{default:a(()=>[e(c,{span:6},{default:a(()=>[e(u,{label:"事件编号"},{default:a(()=>[e(g,{modelValue:o.search.eventId,"onUpdate:modelValue":s[0]||(s[0]=l=>o.search.eventId=l),placeholder:"请输入事件编号",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(c,{span:6},{default:a(()=>[e(u,{label:"事件类型"},{default:a(()=>[e(m,{modelValue:o.search.eventType,"onUpdate:modelValue":s[1]||(s[1]=l=>o.search.eventType=l),placeholder:"请选择事件类型",clearable:""},{default:a(()=>[e(r,{label:"所有",value:""}),e(r,{label:"故障",value:"故障"}),e(r,{label:"告警",value:"告警"}),e(r,{label:"服务请求",value:"服务请求"}),e(r,{label:"安全事件",value:"安全事件"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(c,{span:6},{default:a(()=>[e(u,{label:"事件状态"},{default:a(()=>[e(m,{modelValue:o.search.status,"onUpdate:modelValue":s[2]||(s[2]=l=>o.search.status=l),placeholder:"请选择事件状态",clearable:""},{default:a(()=>[e(r,{label:"所有",value:""}),e(r,{label:"待处理",value:"待处理"}),e(r,{label:"处理中",value:"处理中"}),e(r,{label:"已解决",value:"已解决"}),e(r,{label:"已关闭",value:"已关闭"}),e(r,{label:"已升级",value:"已升级"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(c,{span:6},{default:a(()=>[e(u,{label:"报告人"},{default:a(()=>[e(g,{modelValue:o.search.reporter,"onUpdate:modelValue":s[3]||(s[3]=l=>o.search.reporter=l),placeholder:"请输入报告人",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(v,{gutter:20},{default:a(()=>[e(c,{span:6},{default:a(()=>[e(u,{label:"优先级"},{default:a(()=>[e(m,{modelValue:o.search.priority,"onUpdate:modelValue":s[4]||(s[4]=l=>o.search.priority=l),placeholder:"请选择优先级",clearable:""},{default:a(()=>[e(r,{label:"所有",value:""}),e(r,{label:"紧急",value:"紧急"}),e(r,{label:"高",value:"高"}),e(r,{label:"中",value:"中"}),e(r,{label:"低",value:"低"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(c,{span:6},{default:a(()=>[e(u,{label:"发生时间"},{default:a(()=>[e(T,{modelValue:o.search.dateRange,"onUpdate:modelValue":s[5]||(s[5]=l=>o.search.dateRange=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1}),e(c,{span:12,class:"search-buttons-col"},{default:a(()=>[e(u,null,{default:a(()=>[h("div",K,[e(_,{type:"primary",onClick:d.loadData},{default:a(()=>[e(f,null,{default:a(()=>[e(k)]),_:1}),p("查询 ")]),_:1},8,["onClick"]),e(_,{onClick:d.resetSearch},{default:a(()=>[p("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),B((V(),A(I,{data:o.tableData,border:"",stripe:"",style:{width:"100%"}},{default:a(()=>[e(i,{prop:"eventId",label:"事件编号",width:"120",sortable:""}),e(i,{prop:"title",label:"事件标题","min-width":"200"}),e(i,{prop:"eventType",label:"事件类型",width:"120"}),e(i,{prop:"status",label:"状态",width:"100"},{default:a(l=>[e(y,{type:d.getStatusType(l.row.status)},{default:a(()=>[p(w(l.row.status),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"priority",label:"优先级",width:"100"},{default:a(l=>[e(y,{type:d.getPriorityType(l.row.priority)},{default:a(()=>[p(w(l.row.priority),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"reporter",label:"报告人",width:"120"}),e(i,{prop:"assignee",label:"处理人",width:"120"}),e(i,{prop:"reportTime",label:"报告时间",width:"160",sortable:""}),e(i,{prop:"resolveTime",label:"解决时间",width:"160",sortable:""}),e(i,{label:"操作",width:"200",fixed:"right"},{default:a(l=>[e(_,{size:"small",onClick:C=>d.handleView(l.row)},{default:a(()=>[p("查看")]),_:2},1032,["onClick"]),e(_,{size:"small",type:"primary",onClick:C=>d.handleEdit(l.row)},{default:a(()=>[p("编辑")]),_:2},1032,["onClick"]),e(_,{size:"small",type:"danger",onClick:C=>d.handleDelete(l.row)},{default:a(()=>[p("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[P,o.loading]]),h("div",L,[e(z,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:o.total,"page-size":o.search.pageSize,"current-page":o.search.currentPage,"page-sizes":[10,20,50,100],onSizeChange:d.handleSizeChange,onCurrentChange:d.handleCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])]),_:1})])}const Z=M(j,[["render",O],["__scopeId","data-v-6a4147d6"]]);export{Z as default};
