<template>
  <div class="file-attachments-container">
    <div class="attachments-header">
      <el-divider content-position="left">
        <span class="divider-title">附件管理</span>
      </el-divider>
      <el-button
        type="primary"
        size="small"
        class="refresh-button"
        :loading="refreshLoading"
        @click="handleManualRefresh"
      >
        <el-icon><Refresh /></el-icon>
        刷新附件状态
      </el-button>
    </div>

    <!-- 变更模板下载 -->
    <div class="template-download-section">
      <el-card shadow="hover" class="template-card">
        <div class="template-content">
          <div class="template-selector">
            <span class="template-title" >变更操作表模板下载</span>
            <el-select
              v-model="selectedTemplate"
              placeholder="请选择变更操作表模板"
              style="width: 50%"
              size="small"
              filterable
              clearable
            >
              <el-option
                v-for="template in templateOptions"
                :key="template.id"
                :label="template.template_name"
                :value="template.id"
              />
            </el-select>
            <el-button
              type="primary"
              size="small"
              @click="downloadTemplate"
              :disabled="!selectedTemplate"
              :loading="templateDownloadLoading"
              style="margin-left: 10px"
            >
              <el-icon><Download /></el-icon>
              下载模板
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <el-row :gutter="20">
      <!-- OA流程文件 -->
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="attachment-card">
          <template #header>
            <div class="card-header">
              <span>OA流程</span>
              <el-tag v-if="changeData.oa_process" type="success" size="small">已上传</el-tag>
              <el-tag v-else type="info" size="small">未上传</el-tag>
            </div>
          </template>

          <div class="card-content">
            <div v-if="changeData.oa_process" class="file-info">
              <el-icon><Document /></el-icon>
              <span class="file-name">{{ getFileName(changeData.oa_process_file) }}</span>
            </div>
            <div v-else class="file-placeholder">
              <el-icon><Upload /></el-icon>
              <span>请上传OA流程文件</span>
            </div>

            <div class="action-buttons">
              <el-button
                v-if="changeData.oa_process"
                type="primary"
                size="small"
                @click="downloadFile('oa_process')"
                :loading="downloadLoading.oa_process"
              >
                <el-icon><Download /></el-icon>
                下载
              </el-button>

              <el-upload
                class="upload-button"
                :action="uploadUrl"
                :headers="headers"
                :data="getUploadData('oa_process')"
                :show-file-list="false"
                :before-upload="beforeUpload"
                :http-request="(options) => customUpload(options, 'oa_process')"
              >
                <el-button
                  type="success"
                  size="small"
                  :loading="uploadLoading.oa_process"
                >
                  <el-icon><Upload /></el-icon>
                  {{ changeData.oa_process ? '重新上传' : '上传' }}
                </el-button>
              </el-upload>

              <el-button
                v-if="changeData.oa_process"
                type="danger"
                size="small"
                @click="removeFile('oa_process')"
                :loading="removeLoading.oa_process"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 签字存档文件 -->
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="attachment-card">
          <template #header>
            <div class="card-header">
              <span>签字存档</span>
              <el-tag v-if="changeData.signed_archive" type="success" size="small">已上传</el-tag>
              <el-tag v-else type="info" size="small">未上传</el-tag>
            </div>
          </template>

          <div class="card-content">
            <div v-if="changeData.signed_archive" class="file-info">
              <el-icon><Document /></el-icon>
              <span class="file-name">{{ getFileName(changeData.signed_archive_file) }}</span>
            </div>
            <div v-else class="file-placeholder">
              <el-icon><Upload /></el-icon>
              <span>请上传签字存档文件</span>
            </div>

            <div class="action-buttons">
              <el-button
                v-if="changeData.signed_archive"
                type="primary"
                size="small"
                @click="downloadFile('signed_archive')"
                :loading="downloadLoading.signed_archive"
              >
                <el-icon><Download /></el-icon>
                下载
              </el-button>

              <el-upload
                class="upload-button"
                :action="uploadUrl"
                :headers="headers"
                :data="getUploadData('signed_archive')"
                :show-file-list="false"
                :before-upload="beforeUpload"
                :http-request="(options) => customUpload(options, 'signed_archive')"
              >
                <el-button
                  type="success"
                  size="small"
                  :loading="uploadLoading.signed_archive"
                >
                  <el-icon><Upload /></el-icon>
                  {{ changeData.signed_archive ? '重新上传' : '上传' }}
                </el-button>
              </el-upload>

              <el-button
                v-if="changeData.signed_archive"
                type="danger"
                size="small"
                @click="removeFile('signed_archive')"
                :loading="removeLoading.signed_archive"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 变更操作表文件 -->
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="attachment-card">
          <template #header>
            <div class="card-header">
              <span>变更操作表</span>
              <el-tag v-if="changeData.operation_sheet" type="success" size="small">已上传</el-tag>
              <el-tag v-else type="info" size="small">未上传</el-tag>
            </div>
          </template>

          <div class="card-content">
            <div v-if="changeData.operation_sheet" class="file-info">
              <el-icon><Document /></el-icon>
              <span class="file-name">{{ getFileName(changeData.operation_sheet) }}</span>
            </div>
            <div v-else class="file-placeholder">
              <el-icon><Upload /></el-icon>
              <span>请上传变更操作表文件</span>
            </div>

            <div class="action-buttons">
              <el-button
                v-if="changeData.operation_sheet"
                type="primary"
                size="small"
                @click="downloadFile('operation_sheet')"
                :loading="downloadLoading.operation_sheet"
              >
                <el-icon><Download /></el-icon>
                下载
              </el-button>

              <el-upload
                class="upload-button"
                :action="uploadUrl"
                :headers="headers"
                :data="getUploadData('operation_sheet')"
                :show-file-list="false"
                :before-upload="beforeUpload"
                :http-request="(options) => customUpload(options, 'operation_sheet')"
              >
                <el-button
                  type="success"
                  size="small"
                  :loading="uploadLoading.operation_sheet"
                >
                  <el-icon><Upload /></el-icon>
                  {{ changeData.operation_sheet ? '重新上传' : '上传' }}
                </el-button>
              </el-upload>

              <el-button
                v-if="changeData.operation_sheet"
                type="danger"
                size="small"
                @click="removeFile('operation_sheet')"
                :loading="removeLoading.operation_sheet"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 补充资料文件 -->
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="attachment-card">
          <template #header>
            <div class="card-header">
              <span>补充资料</span>
              <el-tag v-if="changeData.supplementary_material" type="success" size="small">已上传</el-tag>
              <el-tag v-else type="info" size="small">未上传</el-tag>
            </div>
          </template>

          <div class="card-content">
            <div v-if="changeData.supplementary_material" class="file-info">
              <el-icon><Document /></el-icon>
              <span class="file-name">{{ getFileName(changeData.supplementary_material) }}</span>
            </div>
            <div v-else class="file-placeholder">
              <el-icon><Upload /></el-icon>
              <span>请上传补充资料文件</span>
            </div>

            <div class="action-buttons">
              <el-button
                v-if="changeData.supplementary_material"
                type="primary"
                size="small"
                @click="downloadFile('supplementary_material')"
                :loading="downloadLoading.supplementary_material"
              >
                <el-icon><Download /></el-icon>
                下载
              </el-button>

              <el-upload
                class="upload-button"
                :action="uploadUrl"
                :headers="headers"
                :data="getUploadData('supplementary_material')"
                :show-file-list="false"
                :before-upload="beforeUpload"
                :http-request="(options) => customUpload(options, 'supplementary_material')"
              >
                <el-button
                  type="success"
                  size="small"
                  :loading="uploadLoading.supplementary_material"
                >
                  <el-icon><Upload /></el-icon>
                  {{ changeData.supplementary_material ? '重新上传' : '上传' }}
                </el-button>
              </el-upload>

              <el-button
                v-if="changeData.supplementary_material"
                type="danger"
                size="small"
                @click="removeFile('supplementary_material')"
                :loading="removeLoading.supplementary_material"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Download, Upload, Delete, Refresh } from '@element-plus/icons-vue'
import { getToken } from '@/utils/auth'
import request from '@/utils/request'

export default {
  name: 'FileAttachments',
  components: {
    Document,
    Download,
    Upload,
    Delete,
    Refresh
  },
  props: {
    changeData: {
      type: Object,
      required: true
    },
    refreshChangeData: {
      type: Function,
      default: null
    }
  },
  emits: ['update:changeData'],
  setup(props, { emit }) {
    // 上传URL
    const uploadUrl = '/api/upload_ops_change_file_simple'

    // 请求头（包含token）
    const headers = {
      Authorization: `Bearer ${getToken()}`
    }

    // 加载状态
    const uploadLoading = reactive({
      oa_process: false,
      signed_archive: false,
      operation_sheet: false,
      supplementary_material: false
    })

    const downloadLoading = reactive({
      oa_process: false,
      signed_archive: false,
      operation_sheet: false,
      supplementary_material: false
    })

    const removeLoading = reactive({
      oa_process: false,
      signed_archive: false,
      operation_sheet: false,
      supplementary_material: false
    })

    // 刷新加载状态
    const refreshLoading = ref(false)

    // 模板相关数据
    const selectedTemplate = ref('')
    const templateOptions = ref([])
    const templateDownloadLoading = ref(false)

    // 获取上传数据
    const getUploadData = (fileType) => {
      return {
        changeId: props.changeData.change_id,
        fileType: fileType,
        usernameby: localStorage.getItem('username') || 'admin'
      }
    }

    // 从文件路径中提取文件名
    const getFileName = (filePath) => {
      if (!filePath) return ''
      return filePath.split('/').pop()
    }

    // 上传前验证
    const beforeUpload = (file) => {
      // 文件类型验证
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'image/jpeg',
        'image/png'
      ]

      if (!allowedTypes.includes(file.type)) {
        ElMessage.error('文件类型不支持，请上传PDF、Word、Excel或图片文件')
        return false
      }

      // 文件大小验证（20MB）
      const maxSize = 20 * 1024 * 1024
      if (file.size > maxSize) {
        ElMessage.error('文件大小不能超过20MB')
        return false
      }

      return true
    }

    // 刷新变更详情数据
    const refreshChangeData = async (isRetry = false, forceUpdate = false) => {
      try {
        console.log(`开始${isRetry ? '重试' : ''}刷新变更详情数据...${forceUpdate ? '(强制更新模式)' : ''}`)
        console.log('当前变更ID:', props.changeData.id)

        if (!props.changeData.id) {
          console.log('变更ID为空，无法刷新数据')
          return false
        }

        console.log('发送请求获取最新变更数据...')

        // 添加时间戳参数，避免缓存
        const timestamp = new Date().getTime()

        // 使用request函数而不是fetch，确保与其他API请求保持一致
        const requestData = {
          id: props.changeData.id,
          _t: timestamp // 添加时间戳参数，避免缓存
        }
        console.log('请求参数:', requestData)

        // 导入request函数
        const { default: request } = await import('@/utils/request')

        const response = await request({
          url: '/api/get_ops_change_management',
          method: 'post',
          data: requestData,
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        })

        console.log('获取变更数据响应:', JSON.stringify(response, null, 2))

        if (response.code === 0 && response.msg && response.msg.length > 0) {
          const freshData = response.msg[0]
          console.log('获取到的最新数据:', JSON.stringify(freshData, null, 2))
          console.log('附件相关字段:',
            '附件1(OA流程):', freshData.oa_process, freshData.oa_process_file,
            '附件2(签字存档):', freshData.signed_archive, freshData.signed_archive_file,
            '附件3(操作表):', freshData.operation_sheet,
            '附件4(补充资料):', freshData.supplementary_material
          )

          // 创建新的变更数据对象，保留原有数据，更新附件相关字段
          const newChangeData = { ...props.changeData }

          // 更新附件相关字段
          newChangeData.oa_process = freshData.oa_process
          newChangeData.oa_process_file = freshData.oa_process_file
          newChangeData.signed_archive = freshData.signed_archive
          newChangeData.signed_archive_file = freshData.signed_archive_file
          newChangeData.operation_sheet = freshData.operation_sheet
          newChangeData.supplementary_material = freshData.supplementary_material

          console.log('更新前的数据:', {
            oa_process: props.changeData.oa_process,
            oa_process_file: props.changeData.oa_process_file,
            signed_archive: props.changeData.signed_archive,
            signed_archive_file: props.changeData.signed_archive_file,
            operation_sheet: props.changeData.operation_sheet,
            supplementary_material: props.changeData.supplementary_material
          })
          console.log('更新后的数据:', {
            oa_process: newChangeData.oa_process,
            oa_process_file: newChangeData.oa_process_file,
            signed_archive: newChangeData.signed_archive,
            signed_archive_file: newChangeData.signed_archive_file,
            operation_sheet: newChangeData.operation_sheet,
            supplementary_material: newChangeData.supplementary_material
          })

          // 检查数据是否有变化
          const hasChanged =
            props.changeData.oa_process !== newChangeData.oa_process ||
            props.changeData.oa_process_file !== newChangeData.oa_process_file ||
            props.changeData.signed_archive !== newChangeData.signed_archive ||
            props.changeData.signed_archive_file !== newChangeData.signed_archive_file ||
            props.changeData.operation_sheet !== newChangeData.operation_sheet ||
            props.changeData.supplementary_material !== newChangeData.supplementary_material

          console.log('数据是否有变化:', hasChanged)
          console.log('是否强制更新:', forceUpdate)

          // 如果数据有变化或者强制更新模式
          if (hasChanged || forceUpdate) {
            console.log(hasChanged ? '数据有变化，更新组件数据' : '强制更新模式，更新组件数据')

            // 创建一个全新的对象，确保Vue能够检测到变化
            const completelyNewData = JSON.parse(JSON.stringify(newChangeData))

            // 更新组件数据
            emit('update:changeData', completelyNewData)

            // 显示刷新成功消息
            if (!isRetry) {
              ElMessage.success(hasChanged ? '附件状态已更新' : '刷新成功')
            }

            return true
          } else {
            console.log('数据没有变化，无需更新')
            return false
          }
        } else {
          console.log('获取变更数据失败或数据为空')
          return false
        }
      } catch (error) {
        console.error('刷新变更详情数据失败:', error)
        return false
      }
    }

    // 上传成功处理
    const handleUploadSuccess = async (response, fileType) => {
      try {
        console.log('上传成功处理函数被调用')
        console.log('上传响应:', response)

        if (response.code === 0) {
          ElMessage.success('文件上传成功')

          // 更新组件数据
          const newChangeData = { ...props.changeData }

          if (fileType === 'oa_process') {
            newChangeData.oa_process = true
            newChangeData.oa_process_file = response.msg.path
          } else if (fileType === 'signed_archive') {
            newChangeData.signed_archive = true
            newChangeData.signed_archive_file = response.msg.path
          } else if (fileType === 'operation_sheet') {
            newChangeData.operation_sheet = response.msg.path
          } else if (fileType === 'supplementary_material') {
            newChangeData.supplementary_material = response.msg.path
          }

          console.log('更新前的数据:', props.changeData)
          console.log('更新后的数据:', newChangeData)

          emit('update:changeData', newChangeData)

          console.log('组件数据已更新，准备刷新变更详情数据...')

          // 延迟三秒后刷新，确保后端数据已完全更新
          ElMessage.info('正在更新附件状态，请稍候...')
          setTimeout(async () => {
            console.log('开始延迟刷新...')
            try {
              // 优先使用父组件的刷新方法
              if (props.refreshChangeData && typeof props.refreshChangeData === 'function') {
                console.log('使用父组件的刷新方法')
                const result = await props.refreshChangeData()

                if (result) {
                  console.log('父组件刷新成功')
                } else {
                  console.log('父组件刷新失败或无变化，尝试第二次刷新')

                  // 如果第一次刷新未获取到新数据，再延迟两秒进行第二次刷新
                  setTimeout(async () => {
                    console.log('开始第二次刷新...')
                    try {
                      const secondResult = await props.refreshChangeData()
                      console.log('第二次刷新完成，结果:', secondResult)

                      if (!secondResult) {
                        console.log('两次刷新均未获取到更新的数据，可能需要手动刷新')
                        ElMessage.info('附件状态可能需要手动刷新查看')
                      }
                    } catch (error) {
                      console.error('第二次刷新出错:', error)
                    }
                  }, 2000)
                }
              } else {
                console.log('使用组件内部的刷新方法')
                // 第一次刷新
                const firstRefreshResult = await refreshChangeData(false, false)
                console.log('第一次刷新完成，结果:', firstRefreshResult)

                if (!firstRefreshResult) {
                  // 如果第一次刷新没有获取到更新的数据，再延迟两秒进行第二次刷新
                  setTimeout(async () => {
                    console.log('开始第二次刷新...')
                    try {
                      const secondRefreshResult = await refreshChangeData(true, false)
                      console.log('第二次刷新完成，结果:', secondRefreshResult)

                      if (!secondRefreshResult) {
                        // 如果第二次刷新仍然没有获取到更新的数据，再延迟三秒进行第三次刷新，使用强制更新模式
                        setTimeout(async () => {
                          console.log('开始第三次刷新(强制更新模式)...')
                          try {
                            const thirdRefreshResult = await refreshChangeData(true, true)
                            console.log('第三次刷新完成，结果:', thirdRefreshResult)

                            if (!thirdRefreshResult) {
                              console.log('三次刷新均未获取到更新的数据，可能需要手动刷新页面')
                              ElMessage.info('附件状态可能需要手动刷新查看')

                              // 强制重新渲染组件
                              setTimeout(() => {
                                const newChangeData = JSON.parse(JSON.stringify(props.changeData))
                                emit('update:changeData', newChangeData)
                              }, 100)
                            }
                          } catch (refreshError3) {
                            console.error('第三次刷新出错:', refreshError3)
                          }
                        }, 3000)
                      }
                    } catch (refreshError2) {
                      console.error('第二次刷新出错:', refreshError2)
                    }
                  }, 2000)
                }
              }
            } catch (refreshError) {
              console.error('第一次刷新出错:', refreshError)
            }
          }, 3000)
        } else {
          ElMessage.error(`上传失败: ${response.msg}`)
        }
      } catch (error) {
        console.error('上传成功处理函数出错:', error)
      } finally {
        // 重置加载状态
        uploadLoading[fileType] = false
      }
    }

    // 自定义上传方法
    const customUpload = async (options, fileType) => {
      try {
        // 设置加载状态
        uploadLoading[fileType] = true

        console.log('开始上传文件:', fileType)
        console.log('文件信息:', options.file)
        console.log('变更ID:', props.changeData.change_id)

        if (!props.changeData.change_id) {
          throw new Error('变更ID不能为空，请先保存变更信息')
        }

        // 创建FormData对象，只包含文件
        const formData = new FormData()
        formData.append('file', options.file)

        // 构建URL查询参数
        const username = localStorage.getItem('username') || 'admin'
        const queryParams = new URLSearchParams({
          changeId: props.changeData.change_id,
          fileType: fileType,
          usernameby: username
        }).toString()

        console.log('表单数据:', {
          changeId: props.changeData.change_id,
          fileType: fileType,
          usernameby: username
        })

        // 发送请求，使用URL查询参数传递变更ID和文件类型
        const response = await fetch(`${uploadUrl}?${queryParams}`, {
          method: 'POST',
          headers: {
            'Authorization': headers.Authorization
          },
          body: formData
        })

        // 解析响应
        const data = await response.json()

        // 处理响应
        if (response.ok) {
          handleUploadSuccess(data, fileType)
        } else {
          throw new Error(data.msg || '上传失败')
        }
      } catch (error) {
        console.error('上传错误:', error)
        ElMessage.error(`上传失败: ${error.message}`)

        // 重置加载状态
        uploadLoading[fileType] = false
      }
    }

    // 上传错误处理
    const handleUploadError = (error) => {
      console.error('上传错误:', error)
      ElMessage.error('文件上传失败，请稍后重试')

      // 重置所有加载状态
      Object.keys(uploadLoading).forEach(key => {
        uploadLoading[key] = false
      })
    }

    // 下载文件
    const downloadFile = async (fileType) => {
      try {
        downloadLoading[fileType] = true

        // 使用直接下载方式，类似变更模板下载
        const downloadUrl = `/api/download_ops_change_file?changeId=${props.changeData.change_id}&fileType=${fileType}&direct=true`

        // 创建一个隐藏的a标签并模拟点击下载
        const link = document.createElement('a')
        link.href = downloadUrl
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        ElMessage.success('文件下载成功')
      } catch (error) {
        console.error('下载错误:', error)
        ElMessage.error(`文件下载失败: ${error.message}`)
      } finally {
        downloadLoading[fileType] = false
      }
    }

    // 删除文件
    const removeFile = (fileType) => {
      ElMessageBox.confirm(
        '确定要删除此文件吗？此操作不可恢复。',
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          removeLoading[fileType] = true

          const response = await fetch('/api/remove_ops_change_file', {
            method: 'POST',
            headers: {
              ...headers,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              changeId: props.changeData.change_id,
              fileType: fileType,
              usernameby: localStorage.getItem('username') || 'admin'
            })
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.msg || '删除失败')
          }

          const data = await response.json()

          if (data.code === 0) {
            ElMessage.success('文件删除成功')

            console.log('文件删除成功，准备更新组件数据')

            // 更新组件数据
            const newChangeData = { ...props.changeData }

            if (fileType === 'oa_process') {
              newChangeData.oa_process = false
              newChangeData.oa_process_file = null
            } else if (fileType === 'signed_archive') {
              newChangeData.signed_archive = false
              newChangeData.signed_archive_file = null
            } else if (fileType === 'operation_sheet') {
              newChangeData.operation_sheet = null
            } else if (fileType === 'supplementary_material') {
              newChangeData.supplementary_material = null
            }

            console.log('更新前的数据:', props.changeData)
            console.log('更新后的数据:', newChangeData)

            emit('update:changeData', newChangeData)

            console.log('组件数据已更新，准备刷新变更详情数据...')

            // 延迟三秒后刷新，确保后端数据已完全更新
            ElMessage.info('正在更新附件状态，请稍候...')
            setTimeout(async () => {
              console.log('开始延迟刷新...')
              try {
                // 优先使用父组件的刷新方法
                if (props.refreshChangeData && typeof props.refreshChangeData === 'function') {
                  console.log('使用父组件的刷新方法')
                  const result = await props.refreshChangeData()

                  if (result) {
                    console.log('父组件刷新成功')
                  } else {
                    console.log('父组件刷新失败或无变化，尝试第二次刷新')

                    // 如果第一次刷新未获取到新数据，再延迟两秒进行第二次刷新
                    setTimeout(async () => {
                      console.log('开始第二次刷新...')
                      try {
                        const secondResult = await props.refreshChangeData()
                        console.log('第二次刷新完成，结果:', secondResult)

                        if (!secondResult) {
                          console.log('两次刷新均未获取到更新的数据，可能需要手动刷新')
                          ElMessage.info('附件状态可能需要手动刷新查看')
                        }
                      } catch (error) {
                        console.error('第二次刷新出错:', error)
                      }
                    }, 2000)
                  }
                } else {
                  console.log('使用组件内部的刷新方法')
                  // 第一次刷新
                  const firstRefreshResult = await refreshChangeData(false, false)
                  console.log('第一次刷新完成，结果:', firstRefreshResult)

                  if (!firstRefreshResult) {
                    // 如果第一次刷新没有获取到更新的数据，再延迟两秒进行第二次刷新
                    setTimeout(async () => {
                      console.log('开始第二次刷新...')
                      try {
                        const secondRefreshResult = await refreshChangeData(true, false)
                        console.log('第二次刷新完成，结果:', secondRefreshResult)

                        if (!secondRefreshResult) {
                          // 如果第二次刷新仍然没有获取到更新的数据，再延迟三秒进行第三次刷新，使用强制更新模式
                          setTimeout(async () => {
                            console.log('开始第三次刷新(强制更新模式)...')
                            try {
                              const thirdRefreshResult = await refreshChangeData(true, true)
                              console.log('第三次刷新完成，结果:', thirdRefreshResult)

                              if (!thirdRefreshResult) {
                                console.log('三次刷新均未获取到更新的数据，可能需要手动刷新页面')
                                ElMessage.info('附件状态可能需要手动刷新查看')

                                // 强制重新渲染组件
                                setTimeout(() => {
                                  const newChangeData = JSON.parse(JSON.stringify(props.changeData))
                                  emit('update:changeData', newChangeData)
                                }, 100)
                              }
                            } catch (refreshError3) {
                              console.error('第三次刷新出错:', refreshError3)
                            }
                          }, 3000)
                        }
                      } catch (refreshError2) {
                        console.error('第二次刷新出错:', refreshError2)
                      }
                    }, 2000)
                  }
                }
              } catch (refreshError) {
                console.error('第一次刷新出错:', refreshError)
              }
            }, 3000)
          } else {
            throw new Error(data.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除错误:', error)
          ElMessage.error(`文件删除失败: ${error.message}`)
        } finally {
          removeLoading[fileType] = false
        }
      }).catch(() => {
        // 用户取消删除操作
      })
    }

    // 手动刷新方法
    const handleManualRefresh = async () => {
      try {
        refreshLoading.value = true
        ElMessage.info('正在刷新附件状态...')

        // 优先使用父组件的刷新方法
        if (props.refreshChangeData && typeof props.refreshChangeData === 'function') {
          console.log('使用父组件的刷新方法')
          const result = await props.refreshChangeData()

          if (result) {
            console.log('父组件刷新成功')
          } else {
            console.log('父组件刷新失败或无变化')
            ElMessage.info('刷新完成，未检测到附件状态变化')
          }
        } else {
          console.log('使用组件内部的刷新方法')
          // 使用强制更新模式，确保组件数据被更新
          const result = await refreshChangeData(false, true)

          if (result) {
            // 消息已在refreshChangeData中显示
          } else {
            ElMessage.info('刷新完成，未检测到附件状态变化')
          }

          // 强制重新渲染组件
          setTimeout(() => {
            const newChangeData = JSON.parse(JSON.stringify(props.changeData))
            emit('update:changeData', newChangeData)
          }, 100)
        }
      } catch (error) {
        console.error('手动刷新出错:', error)
        ElMessage.error('刷新失败，请稍后重试')
      } finally {
        refreshLoading.value = false
      }
    }

    // 获取模板列表
    const getTemplateList = async () => {
      try {
        const response = await request({
          url: '/api/get_ops_change_templates',
          method: 'post',
          data: {
            currentPage: 1,
            pageSize: 100 // 获取所有模板
          }
        })

        if (response.code === 0) {
          templateOptions.value = response.msg || []
          console.log('获取模板列表成功:', templateOptions.value)
        } else {
          console.error('获取模板列表失败:', response.msg)
        }
      } catch (error) {
        console.error('获取模板列表失败:', error)
      }
    }

    // 下载模板
    const downloadTemplate = async () => {
      if (!selectedTemplate.value) {
        ElMessage.warning('请先选择要下载的模板')
        return
      }

      try {
        templateDownloadLoading.value = true
        console.log('开始下载模板，ID:', selectedTemplate.value)

        // 获取选中的模板信息
        const selectedTemplateInfo = templateOptions.value.find(t => t.id === selectedTemplate.value)
        if (!selectedTemplateInfo) {
          ElMessage.error('未找到选中的模板')
          return
        }

        // 直接下载文件
        const downloadUrl = `/api/download_ops_change_template?id=${selectedTemplate.value}&direct=true`

        // 创建一个隐藏的a标签并模拟点击下载
        const link = document.createElement('a')
        link.href = downloadUrl
        link.target = '_blank'
        link.download = selectedTemplateInfo.original_filename || selectedTemplateInfo.template_name

        // 设置请求头
        const token = getToken()
        if (token) {
          // 对于直接下载，我们需要在URL中包含token或使用其他方式
          // 这里我们使用window.open的方式
          window.open(downloadUrl, '_blank')
        } else {
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }

        ElMessage.success(`模板 "${selectedTemplateInfo.template_name}" 下载成功`)

        // 清空选择
        selectedTemplate.value = ''
      } catch (error) {
        console.error('下载模板失败:', error)
        ElMessage.error('模板下载失败，请稍后重试')
      } finally {
        templateDownloadLoading.value = false
      }
    }

    // 组件挂载时获取模板列表
    onMounted(() => {
      getTemplateList()
    })

    return {
      uploadUrl,
      headers,
      uploadLoading,
      downloadLoading,
      removeLoading,
      refreshLoading,
      selectedTemplate,
      templateOptions,
      templateDownloadLoading,
      getUploadData,
      getFileName,
      beforeUpload,
      handleUploadSuccess,
      handleUploadError,
      customUpload,
      downloadFile,
      removeFile,
      refreshChangeData,
      handleManualRefresh,
      getTemplateList,
      downloadTemplate
    }
  }
}
</script>

<style scoped>
.file-attachments-container {
  margin-top: 20px;
  margin-bottom: 20px;
}

.attachments-header {
  position: relative;
  margin-bottom: 20px;
}

.refresh-button {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
}

/* 模板下载卡片 */
.template-download-section {
  margin-bottom: 20px;
}

.template-card {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.template-content {
  padding: 16px;
}

.template-selector {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 10px;
}

.divider-title {
  font-size: 16px;
  font-weight: bold;
}

.attachment-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.attachment-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.card-content {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.file-info, .file-placeholder {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 4px;
}

.file-info {
  background-color: #f0f9eb;
}

.file-placeholder {
  background-color: #f5f7fa;
  color: #909399;
}

.file-info .el-icon, .file-placeholder .el-icon {
  margin-right: 8px;
  font-size: 20px;
}

.file-name {
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.upload-button {
  margin: 0;
}

@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .action-buttons .el-button {
    width: 100%;
  }

  .template-selector {
    flex-direction: column;
    align-items: stretch;
  }

  .template-selector .el-select {
    width: 100% !important;
  }

  .template-selector .el-button {
    width: 100%;
    margin-left: 0 !important;
    margin-top: 10px;
  }
}
</style>

