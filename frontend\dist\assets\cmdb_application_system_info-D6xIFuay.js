import{_ as N,z as O,v as j,A as G,c as g,a as e,f as v,w as s,b as c,F as V,l as D,h as f,B as L,C as H,m as y,x as J,t as x,e as p}from"./index-Bo9zKuAo.js";import{u as I,w as K,F as Q}from"./FileSaver.min-BlIK9FxR.js";const W={components:{Plus:G,Search:j,Download:O},data(){var r,a,d;return{userArr:[],loading:!1,productionattributes:[],masterslaveroles:[],backupmodes:[],systemclassifications:[],datacenters:[],businessSystems:[],hasDeletePermission:(r=localStorage.getItem("role_code"))==null?void 0:r.includes("D"),hasUpdatePermission:(a=localStorage.getItem("role_code"))==null?void 0:a.includes("U"),hasInsertPermission:(d=localStorage.getItem("role_code"))==null?void 0:d.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{management_ip:"",hostname:"",function_purpose:"",server_admin1:"",server_admin2:"",data_center:"",machine_usage_status:"",business_system_name:"",system_classification:"",is_monitored:"",production_attributes:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,management_ip:"",hostname:"",function_purpose:"",server_admin1:"",server_admin2:"",data_center:"",machine_usage_status:"",remarks:"",business_system_name:"",system_administrator:"",system_classification:"",is_monitored:"",deployed_applications:"",production_attributes:"",master_slave_role:"",related_master_slave_ips:"",backup_mode:"",internet_ip:"",internet_port:"",operating_system:"",has_antivirus_software:"否",patch_update_configured:"否",has_system_administrator:""},rules:{management_ip:[{required:!0,message:"请输入管理IP",trigger:"blur"},{pattern:/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,message:"请输入正确的IP地址格式",trigger:"blur"}],business_system_name:[{required:!0,message:"请选择归属业务系统",trigger:"change"}],master_slave_role:[{required:!0,message:"请选择主从角色",trigger:"change"}],deployed_applications:[{required:!0,message:"请输入部署应用",trigger:"blur"}],production_attributes:[{required:!0,message:"请选择生产属性",trigger:"change"}],related_master_slave_ips:[{required:!1,validator:(k,l,u)=>{if(this.formData.master_slave_role&&!this.isSingleMachine(this.formData.master_slave_role))if(!l)u(new Error("当主从角色不为单机时，关联主从机IP为必填项"));else{const m=l.split(","),o=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;m.filter(_=>!o.test(_.trim())).length>0?u(new Error("请输入正确的IP地址格式，多个IP请用英文逗号分隔")):u()}else u()},trigger:"blur"}]}}},mounted(){this.$route.query.search_ip&&(this.search.management_ip=this.$route.query.search_ip),this.loadData(),this.getDatadict("C","productionattributes"),this.getDatadict("F","masterslaveroles"),this.getDatadict("G","backupmodes"),this.getDatadict("A","datacenters"),this.getDatadict("I","systemclassifications"),this.getBusinessSystems(),this.$route.query.from_discovery==="true"&&this.$nextTick(()=>{this.handleAddFromDiscovery()})},methods:{handlePageChange(r){this.search.currentPage=r,this.loadData()},handlePageSizeChange(r){this.search.pageSize=parseInt(r),this.search.currentPage=1,this.loadData()},handleSortChange({prop:r,order:a}){this.search.sortProp=r,this.search.sortOrder=a==="ascending"?"asc":"desc",this.loadData()},async loadData(){try{this.loading=!0;const r=await this.$axios.post("/api/get_cmdb_application_system_info",this.search);this.userArr=r.data.msg,this.search.total=r.data.total}catch(r){console.error("数据加载失败:",r),this.$message.error("数据加载失败")}finally{this.loading=!1}},async validateAndSubmitAdd(){try{await this.$refs.addFormRef.validate(),await this.submitAdd()}catch{this.$message.error("请完善必填项后再提交")}},async submitAdd(){var r,a;try{const d={...this.formData,usernameby:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/add_cmdb_application_system_info",d),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(d){console.error("添加失败:",d),this.$message.error(((a=(r=d.response)==null?void 0:r.data)==null?void 0:a.msg)||"添加失败")}},async validateAndSubmitEdit(){try{await this.$refs.editFormRef.validate(),await this.submitEdit()}catch{this.$message.error("请完善必填项后再提交")}},async submitEdit(){var r,a;try{const d={...this.formData,usernameby:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/update_cmdb_application_system_info",d),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(d){console.error("更新失败:",d),this.$message.error(((a=(r=d.response)==null?void 0:r.data)==null?void 0:a.msg)||"更新失败")}},async submitDelete(){try{await this.$axios.post("/api/del_cmdb_application_system_info",this.formData),this.$message.success("删除成功"),this.loadData(),this.dialogVisible.delete=!1}catch(r){console.error("删除失败:",r),this.$message.error("删除失败")}},resetSearch(){this.search={management_ip:"",hostname:"",function_purpose:"",server_admin1:"",server_admin2:"",data_center:"",machine_usage_status:"",business_system_name:"",system_classification:"",is_monitored:"",production_attributes:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},async getDatadict(r,a){try{const d=await this.$axios.post("/api/get_cmdb_data_dictionary",{dict_code:r});if(this[a]=d.data.msg,a==="masterslaveroles"){console.log("主从角色字典值:",this.masterslaveroles);const k=this.masterslaveroles.find(l=>l.dict_name==="单机");k&&console.log("单机对应的字典值:",k.dict_code)}}catch(d){console.error("数据加载失败:",d),this.$message.error("数据加载失败")}},handleAdd(){this.dialogVisible.add=!this.dialogVisible.add,this.formData={has_antivirus_software:"否",patch_update_configured:"否",master_slave_role:""},this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields(),this.$refs.addFormRef.clearValidate("related_master_slave_ips")})},handleEdit(r,a){this.dialogVisible.edit=!0,this.formData.id=a.id,this.formData.management_ip=a.management_ip,this.formData.hostname=a.hostname,this.formData.function_purpose=a.function_purpose,this.formData.server_admin1=a.server_admin1,this.formData.server_admin2=a.server_admin2,this.formData.data_center=a.data_center,this.formData.machine_usage_status=a.machine_usage_status,this.formData.remarks=a.remarks,this.formData.business_system_name=a.business_system_name,this.formData.system_administrator=a.system_administrator,this.formData.system_classification=a.system_classification,this.formData.is_monitored=a.is_monitored,this.formData.deployed_applications=a.deployed_applications,this.formData.production_attributes=a.production_attributes,this.formData.master_slave_role=a.master_slave_role,this.formData.related_master_slave_ips=a.related_master_slave_ips,this.formData.backup_mode=a.backup_mode,this.formData.internet_ip=a.internet_ip,this.formData.internet_port=a.internet_port,this.formData.operating_system=a.operating_system,this.formData.has_antivirus_software=a.has_antivirus_software,this.formData.patch_update_configured=a.patch_update_configured,this.formData.has_system_administrator=a.has_system_administrator,this.$refs.editFormRef&&this.$nextTick(()=>{this.$refs.editFormRef.clearValidate(),this.isSingleMachine(this.formData.master_slave_role)&&this.$refs.editFormRef.clearValidate("related_master_slave_ips"),this.validateRelatedMasterSlaveIps()})},handleDelete(r,a){this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=a.id,this.formData.management_ip=a.management_ip},exportData(){const a=this.$refs.table.columns,d=a.map(_=>_.label),k=this.userArr.map(_=>a.map(w=>_[w.property])),l=[d,...k],u=I.aoa_to_sheet(l),m=I.book_new();I.book_append_sheet(m,u,"Sheet1");const o=K(m,{bookType:"xlsx",type:"array"}),i=new Blob([o],{type:"application/octet-stream"});Q.saveAs(i,"应用系统信息.xlsx")},getSingleMachineCode(){const r=this.masterslaveroles.find(a=>a.dict_name==="单机");return r?r.dict_code:"F00047"},isSingleMachine(r){if(!r)return!1;const a=this.getSingleMachineCode();return r===a?!0:r==="单机"},getSingleMachineRequired(){return this.formData.master_slave_role&&!this.isSingleMachine(this.formData.master_slave_role)},async getBusinessSystems(){try{const r=await this.$axios.post("/api/get_cmdb_system_admin_responsibility_company",{currentPage:1,pageSize:1e3,sortProp:"system_abbreviation",sortOrder:"asc"});r.data&&r.data.msg&&(this.businessSystems=r.data.msg.filter(a=>a.system_abbreviation).map(a=>({value:a.system_abbreviation,label:a.system_abbreviation})),this.businessSystems=this.businessSystems.filter((a,d,k)=>d===k.findIndex(l=>l.value===a.value)),console.log("业务系统列表:",this.businessSystems))}catch(r){console.error("获取业务系统列表失败:",r),this.$message.error("获取业务系统列表失败")}},validateRelatedMasterSlaveIps(){this.$nextTick(()=>{this.$refs.addFormRef&&(this.isSingleMachine(this.formData.master_slave_role)?this.$refs.addFormRef.clearValidate("related_master_slave_ips"):this.$refs.addFormRef.validateField("related_master_slave_ips")),this.$refs.editFormRef&&(this.isSingleMachine(this.formData.master_slave_role)?this.$refs.editFormRef.clearValidate("related_master_slave_ips"):this.$refs.editFormRef.validateField("related_master_slave_ips"))})},handleAddFromDiscovery(){this.dialogVisible.add=!0;const{ip_address:r,hostname:a,open_ports:d}=this.$route.query;this.formData={management_ip:r||"",hostname:a||"",business_system_name:"",deployed_applications:d?`开放端口: ${d}`:"",remarks:"",has_antivirus_software:"否",patch_update_configured:"否",master_slave_role:""},this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields(),this.$refs.addFormRef.clearValidate("related_master_slave_ips"),this.$message.info("请完善应用系统信息并提交")}),this.$router.replace({path:this.$route.path})}}},X={class:"user-manage"},Y={class:"dialogdiv"},Z={class:"dialog-footer"},$={class:"dialogdiv"},ee={class:"dialog-footer"},ae={class:"button-container"},te={class:"action-bar unified-action-bar"},le={class:"action-bar-left"},se={class:"action-bar-right"},re={style:{display:"flex","white-space":"nowrap"}},oe={class:"pagination"};function ie(r,a,d,k,l,u){const m=c("el-input"),o=c("el-form-item"),i=c("el-option"),_=c("el-select"),w=c("el-form"),h=c("el-button"),P=c("el-dialog"),F=c("el-alert"),b=c("el-col"),q=c("Search"),C=c("el-icon"),R=c("el-row"),U=c("el-card"),M=c("Plus"),A=c("Download"),n=c("el-table-column"),S=c("el-tag"),z=c("el-table"),B=c("el-pagination"),E=H("loading");return p(),g("div",X,[e(P,{modelValue:l.dialogVisible.add,"onUpdate:modelValue":a[12]||(a[12]=t=>l.dialogVisible.add=t),title:"新增应用系统信息",width:"400","align-center":""},{footer:s(()=>[v("div",Z,[e(h,{onClick:a[11]||(a[11]=t=>l.dialogVisible.add=!1)},{default:s(()=>[f("返回")]),_:1}),e(h,{type:"primary",onClick:u.validateAndSubmitAdd},{default:s(()=>[f("确定")]),_:1},8,["onClick"])])]),default:s(()=>[v("div",Y,[e(w,{model:l.formData,rules:l.rules,ref:"addFormRef","label-position":"right","status-icon":""},{default:s(()=>[e(o,{prop:"management_ip",label:"管理IP:"},{default:s(()=>[e(m,{modelValue:l.formData.management_ip,"onUpdate:modelValue":a[0]||(a[0]=t=>l.formData.management_ip=t),style:{width:"240px"},clearable:"",placeholder:"请输入管理IP"},null,8,["modelValue"])]),_:1}),e(o,{prop:"business_system_name",label:"归属业务系统:",required:""},{default:s(()=>[e(_,{modelValue:l.formData.business_system_name,"onUpdate:modelValue":a[1]||(a[1]=t=>l.formData.business_system_name=t),style:{width:"240px"},placeholder:"请选择归属业务系统",filterable:""},{default:s(()=>[(p(!0),g(V,null,D(l.businessSystems,t=>(p(),y(i,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(o,{prop:"deployed_applications",label:"部署应用:",required:""},{default:s(()=>[e(m,{modelValue:l.formData.deployed_applications,"onUpdate:modelValue":a[2]||(a[2]=t=>l.formData.deployed_applications=t),style:{width:"240px"},placeholder:"请输入部署应用"},null,8,["modelValue"])]),_:1}),e(o,{prop:"production_attributes",label:"生产属性:",required:""},{default:s(()=>[e(_,{modelValue:l.formData.production_attributes,"onUpdate:modelValue":a[3]||(a[3]=t=>l.formData.production_attributes=t),style:{width:"240px"},placeholder:"请选择生产属性"},{default:s(()=>[(p(!0),g(V,null,D(l.productionattributes,t=>(p(),y(i,{key:t.dict_code,label:t.dict_name,value:t.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(o,{prop:"master_slave_role",label:"主从角色:",required:""},{default:s(()=>[e(_,{modelValue:l.formData.master_slave_role,"onUpdate:modelValue":a[4]||(a[4]=t=>l.formData.master_slave_role=t),style:{width:"240px"},placeholder:"请选择主从角色",onChange:u.validateRelatedMasterSlaveIps},{default:s(()=>[(p(!0),g(V,null,D(l.masterslaveroles,t=>(p(),y(i,{key:t.dict_code,label:t.dict_name,value:t.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),e(o,{prop:"related_master_slave_ips",label:"关联主从机IP:",required:u.getSingleMachineRequired()},{default:s(()=>[e(m,{modelValue:l.formData.related_master_slave_ips,"onUpdate:modelValue":a[5]||(a[5]=t=>l.formData.related_master_slave_ips=t),style:{width:"240px"},clearable:"",placeholder:"请输入关联主从机IP，多个IP用英文逗号分隔"},null,8,["modelValue"])]),_:1},8,["required"]),e(o,{prop:"internet_ip",label:"互联网IP:"},{default:s(()=>[e(m,{modelValue:l.formData.internet_ip,"onUpdate:modelValue":a[6]||(a[6]=t=>l.formData.internet_ip=t),style:{width:"240px"},placeholder:"请输入互联网IP"},null,8,["modelValue"])]),_:1}),e(o,{prop:"internet_port",label:"互联网端口:"},{default:s(()=>[e(m,{modelValue:l.formData.internet_port,"onUpdate:modelValue":a[7]||(a[7]=t=>l.formData.internet_port=t),style:{width:"240px"},placeholder:"请输入互联网端口"},null,8,["modelValue"])]),_:1}),e(o,{prop:"has_antivirus_software",label:"是否安装杀毒软件:"},{default:s(()=>[e(_,{modelValue:l.formData.has_antivirus_software,"onUpdate:modelValue":a[8]||(a[8]=t=>l.formData.has_antivirus_software=t),style:{width:"240px"},placeholder:"请选择状态"},{default:s(()=>[e(i,{label:"是",value:"是"}),e(i,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),e(o,{prop:"patch_update_configured",label:"是否配置补丁更新服务:"},{default:s(()=>[e(_,{modelValue:l.formData.patch_update_configured,"onUpdate:modelValue":a[9]||(a[9]=t=>l.formData.patch_update_configured=t),style:{width:"240px"},placeholder:"请选择状态"},{default:s(()=>[e(i,{label:"是",value:"是"}),e(i,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),e(o,{prop:"remarks",label:"备注:"},{default:s(()=>[e(m,{modelValue:l.formData.remarks,"onUpdate:modelValue":a[10]||(a[10]=t=>l.formData.remarks=t),style:{width:"240px"},type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),e(P,{modelValue:l.dialogVisible.edit,"onUpdate:modelValue":a[25]||(a[25]=t=>l.dialogVisible.edit=t),title:"编辑应用系统信息",width:"400","align-center":""},{footer:s(()=>[v("div",ee,[e(h,{onClick:a[24]||(a[24]=t=>l.dialogVisible.edit=!1)},{default:s(()=>[f("取消")]),_:1}),e(h,{type:"primary",onClick:u.validateAndSubmitEdit},{default:s(()=>[f("更新")]),_:1},8,["onClick"])])]),default:s(()=>[v("div",$,[e(w,{model:l.formData,rules:l.rules,ref:"editFormRef","label-position":"right","status-icon":""},{default:s(()=>[e(o,{prop:"management_ip",label:"管理IP:"},{default:s(()=>[e(m,{modelValue:l.formData.management_ip,"onUpdate:modelValue":a[13]||(a[13]=t=>l.formData.management_ip=t),style:{width:"240px"},clearable:"",placeholder:"请输入管理IP"},null,8,["modelValue"])]),_:1}),e(o,{prop:"business_system_name",label:"归属业务系统:",required:""},{default:s(()=>[e(_,{modelValue:l.formData.business_system_name,"onUpdate:modelValue":a[14]||(a[14]=t=>l.formData.business_system_name=t),style:{width:"240px"},placeholder:"请选择归属业务系统",filterable:""},{default:s(()=>[(p(!0),g(V,null,D(l.businessSystems,t=>(p(),y(i,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(o,{prop:"deployed_applications",label:"部署应用:",required:""},{default:s(()=>[e(m,{modelValue:l.formData.deployed_applications,"onUpdate:modelValue":a[15]||(a[15]=t=>l.formData.deployed_applications=t),style:{width:"240px"},placeholder:"请输入部署应用"},null,8,["modelValue"])]),_:1}),e(o,{prop:"production_attributes",label:"生产属性:",required:""},{default:s(()=>[e(_,{modelValue:l.formData.production_attributes,"onUpdate:modelValue":a[16]||(a[16]=t=>l.formData.production_attributes=t),style:{width:"240px"},placeholder:"请选择生产属性"},{default:s(()=>[(p(!0),g(V,null,D(l.productionattributes,t=>(p(),y(i,{key:t.dict_code,label:t.dict_name,value:t.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(o,{prop:"master_slave_role",label:"主从角色:",required:""},{default:s(()=>[e(_,{modelValue:l.formData.master_slave_role,"onUpdate:modelValue":a[17]||(a[17]=t=>l.formData.master_slave_role=t),style:{width:"240px"},placeholder:"请选择主从角色",onChange:u.validateRelatedMasterSlaveIps},{default:s(()=>[(p(!0),g(V,null,D(l.masterslaveroles,t=>(p(),y(i,{key:t.dict_code,label:t.dict_name,value:t.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),e(o,{prop:"related_master_slave_ips",label:"关联主从机IP:",required:u.getSingleMachineRequired()},{default:s(()=>[e(m,{modelValue:l.formData.related_master_slave_ips,"onUpdate:modelValue":a[18]||(a[18]=t=>l.formData.related_master_slave_ips=t),style:{width:"240px"},clearable:"",placeholder:"请输入关联主从机IP，多个IP用英文逗号分隔"},null,8,["modelValue"])]),_:1},8,["required"]),e(o,{prop:"internet_ip",label:"互联网IP:"},{default:s(()=>[e(m,{modelValue:l.formData.internet_ip,"onUpdate:modelValue":a[19]||(a[19]=t=>l.formData.internet_ip=t),style:{width:"240px"},placeholder:"请输入互联网IP"},null,8,["modelValue"])]),_:1}),e(o,{prop:"internet_port",label:"互联网端口:"},{default:s(()=>[e(m,{modelValue:l.formData.internet_port,"onUpdate:modelValue":a[20]||(a[20]=t=>l.formData.internet_port=t),style:{width:"240px"},placeholder:"请输入互联网端口"},null,8,["modelValue"])]),_:1}),e(o,{prop:"has_antivirus_software",label:"是否安装杀毒软件:"},{default:s(()=>[e(_,{modelValue:l.formData.has_antivirus_software,"onUpdate:modelValue":a[21]||(a[21]=t=>l.formData.has_antivirus_software=t),style:{width:"240px"},placeholder:"请选择状态"},{default:s(()=>[e(i,{label:"是",value:"是"}),e(i,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),e(o,{prop:"patch_update_configured",label:"是否配置补丁更新服务:"},{default:s(()=>[e(_,{modelValue:l.formData.patch_update_configured,"onUpdate:modelValue":a[22]||(a[22]=t=>l.formData.patch_update_configured=t),style:{width:"240px"},placeholder:"请选择状态"},{default:s(()=>[e(i,{label:"是",value:"是"}),e(i,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),e(o,{prop:"remarks",label:"备注:"},{default:s(()=>[e(m,{modelValue:l.formData.remarks,"onUpdate:modelValue":a[23]||(a[23]=t=>l.formData.remarks=t),style:{width:"240px"},type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),e(P,{modelValue:l.dialogVisible.delete,"onUpdate:modelValue":a[27]||(a[27]=t=>l.dialogVisible.delete=t),title:"删除管理IP",width:"500","align-center":""},{footer:s(()=>[v("div",null,[e(h,{onClick:a[26]||(a[26]=t=>l.dialogVisible.delete=!1)},{default:s(()=>[f("取消")]),_:1}),e(h,{type:"danger",onClick:u.submitDelete},{default:s(()=>[f("确认删除")]),_:1},8,["onClick"])])]),default:s(()=>[e(F,{type:"warning",title:`确定要删除 IP 为 ${l.formData.management_ip} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),e(U,{class:"search-card"},{default:s(()=>[e(w,{inline:!0},{default:s(()=>[e(R,{gutter:10},{default:s(()=>[e(b,{span:6},{default:s(()=>[e(o,{label:"管理IP"},{default:s(()=>[e(m,{modelValue:l.search.management_ip,"onUpdate:modelValue":a[28]||(a[28]=t=>l.search.management_ip=t),placeholder:"请输入管理IP",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:s(()=>[e(o,{label:"主机名"},{default:s(()=>[e(m,{modelValue:l.search.hostname,"onUpdate:modelValue":a[29]||(a[29]=t=>l.search.hostname=t),placeholder:"请输入主机名",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:s(()=>[e(o,{label:"功能用途"},{default:s(()=>[e(m,{modelValue:l.search.function_purpose,"onUpdate:modelValue":a[30]||(a[30]=t=>l.search.function_purpose=t),placeholder:"请输入功能用途",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:s(()=>[e(o,{label:"管理员1"},{default:s(()=>[e(m,{modelValue:l.search.server_admin1,"onUpdate:modelValue":a[31]||(a[31]=t=>l.search.server_admin1=t),placeholder:"请输入管理员1",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:s(()=>[e(o,{label:"管理员2"},{default:s(()=>[e(m,{modelValue:l.search.server_admin2,"onUpdate:modelValue":a[32]||(a[32]=t=>l.search.server_admin2=t),placeholder:"管理员2",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:s(()=>[e(o,{label:"所属机房"},{default:s(()=>[e(_,{modelValue:l.search.data_center,"onUpdate:modelValue":a[33]||(a[33]=t=>l.search.data_center=t),placeholder:"请选择所属机房",class:"form-control"},{default:s(()=>[e(i,{label:"所有",value:""}),(p(!0),g(V,null,D(l.datacenters,t=>(p(),y(i,{key:t.dict_code,label:t.dict_name,value:t.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:s(()=>[e(o,{label:"是否在线"},{default:s(()=>[e(_,{modelValue:l.search.machine_usage_status,"onUpdate:modelValue":a[34]||(a[34]=t=>l.search.machine_usage_status=t),placeholder:"请选择是否在线",class:"form-control"},{default:s(()=>[e(i,{label:"所有",value:""}),e(i,{label:"在线",value:"在线"}),e(i,{label:"离线",value:"离线"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:s(()=>[e(o,{label:"归属业务系统"},{default:s(()=>[e(_,{modelValue:l.search.business_system_name,"onUpdate:modelValue":a[35]||(a[35]=t=>l.search.business_system_name=t),placeholder:"请选择归属业务系统",filterable:"",clearable:"",class:"form-control"},{default:s(()=>[e(i,{label:"所有",value:""}),(p(!0),g(V,null,D(l.businessSystems,t=>(p(),y(i,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:s(()=>[e(o,{label:"系统分级"},{default:s(()=>[e(_,{modelValue:l.search.system_classification,"onUpdate:modelValue":a[36]||(a[36]=t=>l.search.system_classification=t),placeholder:"请选择系统分级",class:"form-control"},{default:s(()=>[e(i,{label:"所有",value:""}),(p(!0),g(V,null,D(l.systemclassifications,t=>(p(),y(i,{key:t.dict_code,label:t.dict_name,value:t.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:s(()=>[e(o,{label:"是否监控"},{default:s(()=>[e(_,{modelValue:l.search.is_monitored,"onUpdate:modelValue":a[37]||(a[37]=t=>l.search.is_monitored=t),placeholder:"请选择是否监控",class:"form-control"},{default:s(()=>[e(i,{label:"所有",value:""}),e(i,{label:"是",value:"是"}),e(i,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:s(()=>[e(o,{label:"生产属性"},{default:s(()=>[e(_,{modelValue:l.search.production_attributes,"onUpdate:modelValue":a[38]||(a[38]=t=>l.search.production_attributes=t),placeholder:"请选择生产属性",class:"form-control"},{default:s(()=>[e(i,{label:"所有",value:""}),(p(!0),g(V,null,D(l.productionattributes,t=>(p(),y(i,{key:t.dict_code,label:t.dict_name,value:t.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6,class:"search-buttons-col"},{default:s(()=>[e(o,{label:" ",class:"form-item-with-label search-buttons"},{default:s(()=>[v("div",ae,[e(h,{type:"primary",onClick:u.loadData},{default:s(()=>[e(C,null,{default:s(()=>[e(q)]),_:1}),f("查询 ")]),_:1},8,["onClick"]),e(h,{onClick:u.resetSearch},{default:s(()=>[f("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),v("div",te,[v("div",le,[e(h,{type:"success",disabled:!l.hasInsertPermission,onClick:u.handleAdd},{default:s(()=>[e(C,null,{default:s(()=>[e(M)]),_:1}),f("新增资产 ")]),_:1},8,["disabled","onClick"])]),v("div",se,[e(h,{type:"info",onClick:u.exportData},{default:s(()=>[e(C,null,{default:s(()=>[e(A)]),_:1}),f(" 导出数据 ")]),_:1},8,["onClick"])])]),e(U,{class:"table-card"},{default:s(()=>[L((p(),y(z,{data:l.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:u.handleSortChange},{default:s(()=>[J("",!0),e(n,{prop:"management_ip",label:"管理IP",sortable:""}),e(n,{prop:"hostname",label:"主机名",sortable:""}),e(n,{prop:"function_purpose",label:"功能用途",sortable:""}),e(n,{prop:"server_admin1",label:"管理员1",sortable:""}),e(n,{prop:"server_admin2",label:"管理员2",sortable:""}),e(n,{prop:"data_center",label:"所属机房",sortable:""}),e(n,{prop:"machine_usage_status",label:"是否在线",sortable:""},{default:s(t=>[e(S,{type:t.row.machine_usage_status==="在线"?"success":"danger"},{default:s(()=>[f(x(t.row.machine_usage_status),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"business_system_name",label:"归属业务系统",sortable:""}),e(n,{prop:"system_administrator",label:"系统管理员",sortable:""}),e(n,{prop:"system_classification",label:"系统分级",sortable:""}),e(n,{prop:"is_monitored",label:"是否监控",sortable:""},{default:s(t=>[e(S,{type:t.row.is_monitored==="是"?"success":"danger"},{default:s(()=>[f(x(t.row.is_monitored),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"deployed_applications",label:"部署应用",sortable:""}),e(n,{prop:"production_attributes",label:"生产属性",sortable:""}),e(n,{prop:"master_slave_role",label:"主从角色",sortable:""}),e(n,{prop:"related_master_slave_ips",label:"关联主从机IP",sortable:""}),e(n,{prop:"internet_ip",label:"互联网IP",sortable:""}),e(n,{prop:"internet_port",label:"互联网端口",sortable:""}),e(n,{prop:"operating_system",label:"操作系统",sortable:""}),e(n,{prop:"has_antivirus_software",label:"是否安装杀毒软件",sortable:""},{default:s(t=>[e(S,{type:t.row.has_antivirus_software==="是"?"success":"danger"},{default:s(()=>[f(x(t.row.has_antivirus_software),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"patch_update_configured",label:"是否配置补丁更新服务",sortable:""},{default:s(t=>[e(S,{type:t.row.patch_update_configured==="是"?"success":"danger"},{default:s(()=>[f(x(t.row.patch_update_configured),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"has_system_administrator",label:"系统级别是否有管理员",sortable:""}),e(n,{prop:"remarks",label:"备注",sortable:""}),e(n,{prop:"created_at",label:"创建时间",sortable:""}),e(n,{prop:"created_by",label:"创建人",sortable:""}),e(n,{prop:"updated_at",label:"更新时间",sortable:""}),e(n,{prop:"updated_by",label:"更新人",sortable:""}),e(n,{label:"操作",align:"center",fixed:"right"},{default:s(t=>[v("div",re,[e(h,{size:"small",type:"warning",disabled:!l.hasUpdatePermission,onClick:T=>u.handleEdit(t.$index,t.row)},{default:s(()=>[f("编辑")]),_:2},1032,["disabled","onClick"]),e(h,{size:"small",type:"danger",disabled:!l.hasDeletePermission,onClick:T=>u.handleDelete(t.$index,t.row)},{default:s(()=>[f("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[E,l.loading]]),v("div",oe,[e(B,{background:"","current-page":l.search.currentPage,"page-size":l.search.pageSize,total:l.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:u.handlePageSizeChange,onCurrentChange:u.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const ue=N(W,[["render",ie],["__scopeId","data-v-05b97e70"]]);export{ue as default};
